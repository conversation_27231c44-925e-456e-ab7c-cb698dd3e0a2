/* EMSManagement 全屏样式 */
.ems-container {
    height: 100vh;
    width: 100vw;
    margin: 0;
    padding: 0;
    overflow: hidden;
    background: linear-gradient(135deg, #0f1419 0%, #1a2332 50%, #0f1419 100%);
}

.fullscreen-iframe {
    width: 100%;
    height: 100%;
    border: none;
    margin: 0;
    padding: 0;
    display: block;
    background: white;
}

/* 确保父级容器也没有边距和填充 */
body.fullscreen-mode {
    margin: 0 !important;
    padding: 0 !important;
    overflow: hidden !important;
    height: 100vh !important;
    width: 100vw !important;
}

/* 确保根元素也是全屏 */
#root.fullscreen-mode {
    margin: 0 !important;
    padding: 0 !important;
    height: 100vh !important;
    width: 100vw !important;
    overflow: hidden !important;
}

/* 隐藏可能的滚动条 */
.ems-container::-webkit-scrollbar {
    display: none;
}

.ems-container {
    -ms-overflow-style: none;
    scrollbar-width: none;
}

/* 移除任何可能的边框或轮廓 */
.fullscreen-iframe:focus {
    outline: none;
    border: none;
}

/* 确保iframe加载时的平滑过渡 */
.fullscreen-iframe {
    transition: opacity 0.3s ease-in-out;
    opacity: 1;
}

/* 加载状态 */
.fullscreen-iframe[src=""] {
    opacity: 0;
}

/* 响应式处理 */
@media (max-width: 768px) {
    .ems-container {
        width: 100vw;
        height: 100vh;
        position: fixed;
        top: 0;
        left: 0;
    }
    
    .fullscreen-iframe {
        width: 100%;
        height: 100%;
    }
}

/* 确保在所有设备上都能正确显示 */
@media screen and (orientation: landscape) {
    .ems-container {
        width: 100vw;
        height: 100vh;
    }
}

@media screen and (orientation: portrait) {
    .ems-container {
        width: 100vw;
        height: 100vh;
    }
}

/* 处理可能的浏览器默认样式 */
* {
    box-sizing: border-box;
}

/* 确保在移动设备上的全屏效果 */
@media screen and (max-device-width: 480px) {
    .ems-container {
        width: 100vw;
        height: 100vh;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
    }
    
    .fullscreen-iframe {
        width: 100vw;
        height: 100vh;
    }
}

/* 处理iOS Safari的特殊情况 */
@supports (-webkit-touch-callout: none) {
    .ems-container {
        height: -webkit-fill-available;
        min-height: 100vh;
    }
    
    .fullscreen-iframe {
        height: -webkit-fill-available;
        min-height: 100vh;
    }
}

/* 全屏模式样式 */
.ems-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    margin: 0;
    padding: 0;
    z-index: 9999;
    background: #000;
}

.fullscreen-iframe {
    width: 100%;
    height: 100%;
    border: none;
    outline: none;
    display: block;
}

/* 只读模式容器样式 */
.ems-readonly-container {
    height: 100vh;
    width: 100vw;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background: linear-gradient(135deg, #0f1419 0%, #1a2332 50%, #0f1419 100%);
    font-family: 'Microsoft YaHei', 'PingFang SC', sans-serif;
}

.ems-header {
    background: linear-gradient(135deg, rgba(15, 20, 25, 0.95) 0%, rgba(26, 35, 50, 0.95) 100%);
    backdrop-filter: blur(10px);
    border-bottom: 2px solid rgba(0, 150, 255, 0.3);
    padding: 8px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 20px rgba(0, 150, 255, 0.2);
    z-index: 1000;
    flex-shrink: 0;
    height: 50px;
}

.header-left h1 {
    color: #ffffff;
    font-size: 1.3em;
    margin: 0;
    background: linear-gradient(135deg, #00c6ff 0%, #0072ff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.ems-iframe-container {
    flex: 1;
    width: 100%;
    height: calc(100vh - 50px);
    border: none;
    border-radius: 0 0 8px 8px;
    overflow: hidden;
    background: #fff;
}

.readonly-header {
    background: linear-gradient(135deg, rgba(15, 20, 25, 0.95) 0%, rgba(26, 35, 50, 0.95) 100%);
    backdrop-filter: blur(10px);
    border-bottom: 2px solid rgba(0, 150, 255, 0.3);
    padding: 15px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 20px rgba(0, 150, 255, 0.2);
    z-index: 1000;
    flex-shrink: 0;
}

.header-content {
    display: flex;
    align-items: center;
    gap: 20px;
    flex-wrap: wrap;
}

.readonly-header h1 {
    color: #333;
    font-size: 24px;
    font-weight: 600;
    margin: 0;
}

.readonly-badge {
    display: flex;
    align-items: center;
    gap: 8px;
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
    padding: 8px 16px;
    border-radius: 25px;
    font-size: 13px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 3px 10px rgba(255, 107, 107, 0.3);
}

.readonly-icon {
    font-size: 16px;
}

.header-actions {
    display: flex;
    gap: 12px;
    align-items: center;
    flex-wrap: wrap;
}

.external-link-btn, .login-btn-header {
    padding: 10px 18px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    white-space: nowrap;
}

.external-link-btn {
    background: #28a745;
    color: white;
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

.external-link-btn:hover {
    background: #218838;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4);
}

.login-btn-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.login-btn-header:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.readonly-iframe-container {
    flex: 1;
    position: relative;
    overflow: hidden;
}

.readonly-iframe {
    width: 100%;
    height: 100%;
    border: none;
    background: white;
}

.readonly-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 100;
}

.readonly-notice {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    padding: 25px 30px;
    border-radius: 15px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.2);
    border: 2px solid rgba(255, 107, 107, 0.3);
    text-align: center;
    max-width: 350px;
    animation: fadeInScale 0.5s ease-out;
}

.readonly-notice h4 {
    margin: 0 0 12px 0;
    color: #dc3545;
    font-size: 18px;
    font-weight: 600;
}

.readonly-notice p {
    margin: 0 0 8px 0;
    color: #6c757d;
    font-size: 14px;
    line-height: 1.5;
}

.readonly-notice p:last-of-type {
    margin-bottom: 20px;
}

.readonly-notice .login-link {
    display: inline-block;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 12px 24px;
    border-radius: 25px;
    text-decoration: none;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 3px 12px rgba(102, 126, 234, 0.3);
}

.readonly-notice .login-link:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

/* 动画效果 */
@keyframes fadeInScale {
    0% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.8);
    }
    100% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .readonly-header {
        flex-direction: column;
        gap: 15px;
        padding: 15px 20px;
    }

    .header-content {
        flex-direction: column;
        gap: 10px;
        align-items: flex-start;
        width: 100%;
    }

    .readonly-header h1 {
        font-size: 20px;
    }

    .readonly-badge {
        padding: 6px 12px;
        font-size: 12px;
    }

    .header-actions {
        width: 100%;
        justify-content: center;
    }

    .readonly-iframe-container {
        margin: 10px;
    }

    .readonly-notice {
        max-width: 280px;
        padding: 20px 25px;
    }

    .readonly-notice h4 {
        font-size: 16px;
    }

    .readonly-notice p {
        font-size: 13px;
    }

    .external-link-btn, .login-btn-header {
        padding: 12px 16px;
        font-size: 13px;
    }
}

@media (max-width: 480px) {
    .header-actions {
        flex-direction: column;
        gap: 10px;
    }

    .external-link-btn, .login-btn-header {
        width: 100%;
        justify-content: center;
        padding: 14px 20px;
    }

    .readonly-notice {
        max-width: 250px;
        padding: 18px 20px;
    }

    .readonly-notice h4 {
        font-size: 15px;
    }

    .readonly-notice p {
        font-size: 12px;
    }

    .readonly-notice .login-link {
        padding: 10px 20px;
        font-size: 13px;
    }
}

/* 移动设备横屏适配 */
@media (max-height: 500px) and (orientation: landscape) {
    .readonly-header {
        padding: 10px 20px;
    }

    .readonly-header h1 {
        font-size: 18px;
    }

    .readonly-badge {
        padding: 4px 8px;
        font-size: 11px;
    }

    .readonly-iframe-container {
        margin: 10px;
    }

    .readonly-notice {
        max-width: 300px;
        padding: 15px 20px;
    }

    .readonly-notice h4 {
        font-size: 14px;
        margin-bottom: 8px;
    }

    .readonly-notice p {
        font-size: 12px;
        margin-bottom: 6px;
    }

    .readonly-notice p:last-of-type {
        margin-bottom: 15px;
    }

    .readonly-notice .login-link {
        padding: 8px 16px;
        font-size: 12px;
    }
} 