.home-view {
    height: 100vh;
    display: flex;
    flex-direction: column;
  
    overflow: hidden;
    font-family: 'Microsoft YaHei', 'PingFang SC', sans-serif;
}

/* 全局标题栏 */
.global-header {
    background: linear-gradient(135deg, #0a0e1a 0%, #1a2332 50%, #0a0e1a 100%);
    border-bottom: 2px solid rgba(0, 150, 255, 0.4);
    padding: 8px 30px;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0 4px 25px rgba(0, 150, 255, 0.3);
    z-index: 1001;
    height: 60px;
    flex-shrink: 0;
    position: relative;
    overflow: hidden;
}

/* 科技感背景装饰 */
.global-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        linear-gradient(90deg, transparent 0%, rgba(0, 200, 255, 0.1) 20%, rgba(0, 200, 255, 0.3) 50%, rgba(0, 200, 255, 0.1) 80%, transparent 100%),
        repeating-linear-gradient(
            90deg,
            transparent,
            transparent 98px,
            rgba(0, 200, 255, 0.03) 100px
        );
    animation: scan 3s linear infinite;
}

/* 角落装饰 */
.global-header::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
        /* 左上角 */
        linear-gradient(45deg, #00c6ff 0%, transparent 50%),
        linear-gradient(-45deg, #00c6ff 0%, transparent 50%),
        /* 右上角 */
        linear-gradient(135deg, #00c6ff 0%, transparent 50%),
        linear-gradient(-135deg, #00c6ff 0%, transparent 50%);
    background-size: 20px 20px, 20px 20px, 20px 20px, 20px 20px;
    background-position: 
        0 0,
        0 0, 
        100% 0,
        100% 0;
    background-repeat: no-repeat;
    opacity: 0.3;
    animation: cornerPulse 3s ease-in-out infinite;
}

@keyframes cornerPulse {
    0%, 100% { opacity: 0.2; }
    50% { opacity: 0.5; }
}

@keyframes scan {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* 全局标题容器 */
.global-title-container {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 12px 50px;
    background: linear-gradient(135deg, rgba(0, 150, 255, 0.08) 0%, rgba(0, 200, 255, 0.05) 100%);
    border-radius: 25px;
    backdrop-filter: blur(15px);
    z-index: 1002;
    overflow: hidden;
}

/* 外层边框效果 */
.global-title-container::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;

    border-radius: 27px;
    z-index: -1;
    animation: borderRotate 3s linear infinite;
}

/* 内层发光效果 */
.global-title-container::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, transparent 0%, rgba(0, 200, 255, 0.1) 50%, transparent 100%);
    border-radius: 25px;
    z-index: -1;
    animation: innerGlow 2s ease-in-out infinite alternate;
}

@keyframes innerGlow {
    0% { opacity: 0.3; }
    100% { opacity: 0.8; }
}

/* 标题左右装饰 - 改为更科技的样式 */
.title-decoration-left,
.title-decoration-right {
    position: absolute;
    top: 50%;
    width: 100px;
    height: 2px;
    background: linear-gradient(90deg, transparent, #00c6ff, #0072ff, #00c6ff, transparent);
    transform: translateY(-50%);
    z-index: 1001;
    box-shadow: 0 0 10px rgba(0, 200, 255, 0.5);
}

.title-decoration-left {
    left: calc(50% - 300px);
    animation: decorationPulse 2s ease-in-out infinite;
}

.title-decoration-right {
    right: calc(50% - 300px);
    animation: decorationPulse 2s ease-in-out infinite 1s;
}

@keyframes decorationPulse {
    0%, 100% { 
        opacity: 0.5;
        transform: translateY(-50%) scaleX(0.8);
        box-shadow: 0 0 5px #00c6ff;
    }
    50% { 
        opacity: 1;
        transform: translateY(-50%) scaleX(1.2);
        box-shadow: 0 0 15px #00c6ff;
    }
}

/* 全局标题文字 */
.global-title {
    font-size: 1.8em;
    font-weight: 700;
    margin: 0;
    background: linear-gradient(135deg, #ffffff 0%, #00c6ff 30%, #0072ff 60%, #00d4ff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 30px rgba(0, 200, 255, 0.5);
    letter-spacing: 3px;
    position: relative;
    z-index: 1003;
    animation: titleGlow 3s ease-in-out infinite alternate;
}

@keyframes titleGlow {
    0% { 
        filter: drop-shadow(0 0 5px rgba(0, 200, 255, 0.5));
    }
    100% { 
        filter: drop-shadow(0 0 15px rgba(0, 200, 255, 0.8));
    }
}

/* 系统状态指示器 */
.system-status {
    position: absolute;
    right: 30px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9em;
    color: rgba(255, 255, 255, 0.8);
    z-index: 1002;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #00ff88;
    box-shadow: 0 0 10px #00ff88;
    animation: statusPulse 2s ease-in-out infinite;
}

@keyframes statusPulse {
    0%, 100% { 
        transform: scale(1);
        box-shadow: 0 0 10px #00ff88;
    }
    50% { 
        transform: scale(1.2);
        box-shadow: 0 0 20px #00ff88;
    }
}

/* 顶部导航栏 */
.top-navigation {
    background: linear-gradient(135deg, rgba(15, 20, 25, 0.95) 0%, rgba(26, 35, 50, 0.95) 100%);
    backdrop-filter: blur(10px);
    border-bottom: 2px solid rgba(0, 150, 255, 0.3);
    padding: 6px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 20px rgba(0, 150, 255, 0.2);
    z-index: 1000;
    height: 50px;
    flex-shrink: 0;
}

.nav-left {
    display: flex;
    align-items: center;
    gap: 15px;
    min-width: 200px;
}

.nav-center {
    flex: 1;
    display: flex;
    justify-content: center;
}

.nav-right {
    display: flex;
    align-items: center;
    gap: 15px;
    min-width: 200px;
    justify-content: flex-end;
}

/* 时间显示美化 */
.current-time {
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
    color: #00c6ff;
    background: linear-gradient(135deg, rgba(0, 200, 255, 0.1) 0%, rgba(0, 150, 255, 0.1) 100%);
    padding: 4px 12px;
    border-radius: 15px;
    border: 1px solid rgba(0, 200, 255, 0.3);
    backdrop-filter: blur(10px);
    text-shadow: 0 0 10px rgba(0, 200, 255, 0.5);
    box-shadow: 0 0 15px rgba(0, 200, 255, 0.2);
    animation: timePulse 2s ease-in-out infinite alternate;
}

@keyframes timePulse {
    0% { 
        box-shadow: 0 0 15px rgba(0, 200, 255, 0.2);
        transform: scale(1);
    }
    100% { 
        box-shadow: 0 0 25px rgba(0, 200, 255, 0.4);
        transform: scale(1.02);
    }
}

.system-title {
    font-size: 1.5em;
    font-weight: 700;
    background: linear-gradient(135deg, #00c6ff 0%, #0072ff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin: 0;
    text-shadow: 0 0 20px rgba(0, 150, 255, 0.3);
}

.system-status {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 0.8em;
    font-weight: 500;
    background: rgba(0, 150, 255, 0.1);
    border: 1px solid rgba(0, 150, 255, 0.3);
}

.system-status.online {
    color: #00ff88;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #00ff88;
    animation: pulse 2s infinite;
    box-shadow: 0 0 10px #00ff88;
}

@keyframes pulse {
    0% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.3); opacity: 0.7; }
    100% { transform: scale(1); opacity: 1; }
}

/* 中央模块选项卡 */
.module-tabs {
    display: flex;
    gap: 5px;
    background: rgba(0, 150, 255, 0.1);
    padding: 5px;
    border-radius: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(0, 150, 255, 0.2);
}

.module-tab {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 16px;
    border: none;
    border-radius: 15px;
    background: transparent;
    color: rgba(255, 255, 255, 0.7);
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    font-size: 0.85em;
    position: relative;
    overflow: hidden;
}

.module-tab::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 150, 255, 0.2) 0%, rgba(0, 200, 255, 0.2) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 15px;
}

.module-tab:hover::before {
    opacity: 1;
}

.module-tab.active {
    color: #ffffff;
    background: linear-gradient(135deg, rgba(0, 150, 255, 0.4) 0%, rgba(0, 200, 255, 0.4) 100%);
    box-shadow: 0 0 15px rgba(0, 150, 255, 0.4);
    border: 1px solid rgba(0, 200, 255, 0.5);
}

.tab-icon {
    font-size: 1em;
    position: relative;
    z-index: 1;
}

.tab-name {
    font-size: 0.85em;
    position: relative;
    z-index: 1;
    white-space: nowrap;
}

/* 右侧用户信息 */
.user-info {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
}

.user-name {
    font-weight: 600;
    color: #ffffff;
    font-size: 0.9em;
}

.user-role {
    font-size: 0.75em;
    color: rgba(255, 255, 255, 0.6);
}

/* 退出按钮样式优化 */
.logout-btn {
    background: linear-gradient(135deg, rgba(255, 69, 69, 0.8) 0%, rgba(220, 38, 127, 0.8) 100%);
    border: 1px solid rgba(255, 69, 69, 0.5);
    color: white;
    padding: 6px 15px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 0.85em;
    font-weight: 600;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 10px rgba(255, 69, 69, 0.3);
}

.logout-btn:hover {
    background: linear-gradient(135deg, rgba(255, 69, 69, 1) 0%, rgba(220, 38, 127, 1) 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(255, 69, 69, 0.5);
}

/* 项目结构按钮特殊样式 */
.project-structure-btn {
    background: linear-gradient(135deg, rgba(0, 212, 255, 0.8) 0%, rgba(116, 75, 162, 0.8) 100%) !important;
    border: 1px solid rgba(0, 212, 255, 0.5) !important;
    margin-left: 10px;
    position: relative;
    overflow: hidden;
}

.project-structure-btn::before {
    content: '🌳';
    margin-right: 6px;
    font-size: 14px;
}

.project-structure-btn:hover {
    background: linear-gradient(135deg, rgba(0, 212, 255, 1) 0%, rgba(116, 75, 162, 1) 100%) !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 212, 255, 0.5) !important;
}

.project-structure-btn::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.project-structure-btn:hover::after {
    left: 100%;
}

/* 主要内容区域 */
.main-content {
    flex: 1;
    overflow: hidden;
    width: 100%;
    height: calc(100vh - 110px);
    display: flex;
    flex-direction: column;
}

/* 底部状态栏 */
.status-bar {
    background: linear-gradient(135deg, rgba(15, 20, 25, 0.95) 0%, rgba(26, 35, 50, 0.95) 100%);
    backdrop-filter: blur(10px);
    border-top: 2px solid rgba(0, 150, 255, 0.3);
    padding: 8px 30px;
    display: flex;
    justify-content: space-around;
    align-items: center;
}

.status-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 3px;
}

.status-label {
    font-size: 0.7em;
    color: rgba(255, 255, 255, 0.6);
    font-weight: 500;
}

.status-value {
    font-weight: 700;
    color: #00c6ff;
    font-size: 0.9em;
    text-shadow: 0 0 10px rgba(0, 198, 255, 0.3);
}

/* 大屏监控样式 */
.dashboard-overview {
    width: 100%;
    height: 100%;
    padding: 15px;
    background: url('/img/load-bg.png') center center / cover no-repeat,
                linear-gradient(135deg, #0f1419 0%, #1a2332 50%, #0f1419 100%);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* 标题栏 */
.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 20px;
    background: linear-gradient(135deg, rgba(0, 150, 255, 0.1) 0%, rgba(0, 200, 255, 0.1) 100%);
    border: 2px solid rgba(0, 150, 255, 0.3);
    border-radius: 10px;
    margin-bottom: 15px;
    box-shadow: 0 0 20px rgba(0, 150, 255, 0.2);
    height: 50px;
    flex-shrink: 0;
}

.header-left, .header-right {
    display: flex;
    align-items: center;
    gap: 15px;
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9em;
}

.header-center h1 {
    color: #ffffff;
    font-size: 2em;
    margin: 0;
    text-align: center;
    background: linear-gradient(135deg, #00c6ff 0%, #0072ff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 30px rgba(0, 150, 255, 0.5);
}

.header-icon {
    font-size: 1.2em;
    color: #00c6ff;
}

/* 仪表盘网格 */
.dashboard-grid {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    grid-template-rows: 1fr 1fr;
    gap: 15px;
    flex: 1;
    height: calc(100vh - 60px - 50px - 50px - 45px);
    min-height: 600px;
    overflow: hidden;
    padding: 15px;
    grid-template-areas: 
        "device-status map production"
        "custom1 trend custom2";
}

.dashboard-card {
    background: linear-gradient(135deg, rgba(15, 20, 25, 0.85) 0%, rgba(26, 35, 50, 0.85) 100%);
    border: 2px solid rgba(0, 150, 255, 0.4);
    border-radius: 16px;
    padding: 16px;
    backdrop-filter: blur(15px);
    box-shadow: 
        0 8px 32px rgba(0, 150, 255, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.dashboard-card:hover {
    transform: translateY(-2px);
    box-shadow: 
        0 12px 40px rgba(0, 150, 255, 0.25),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
    border-color: rgba(0, 150, 255, 0.6);
}

.card-title {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #00c6ff;
    font-weight: 700;
    font-size: 1em;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 2px solid rgba(0, 150, 255, 0.4);
    flex-shrink: 0;
    text-shadow: 0 0 10px rgba(0, 198, 255, 0.5);
    position: relative;
}

.card-title::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 40px;
    height: 2px;
    background: linear-gradient(90deg, #00c6ff 0%, #00ff88 100%);
    border-radius: 1px;
    box-shadow: 0 0 8px rgba(0, 198, 255, 0.6);
}

.title-icon {
    font-size: 1.2em;
    color: #00ff88;
}

/* 网格区域定位 */
.device-status-card { grid-area: device-status; }
.map-container { grid-area: map; }
.production-structure-card { grid-area: production; }
.power-trend { 
    grid-area: trend;
}
.energy-efficiency { grid-area: efficiency; }

/* 自定义区域定位 */
.custom-area-1 { grid-area: custom1; }
.custom-area-2 { grid-area: custom2; }

/* 自定义区域样式 */
.custom-area-1, .custom-area-2 {
    background: linear-gradient(135deg, rgba(15, 20, 25, 0.85) 0%, rgba(26, 35, 50, 0.85) 100%);
    border: 1px solid rgba(0, 255, 136, 0.4);
    border-radius: 12px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(8px);
    box-shadow: 0 4px 20px rgba(0, 255, 136, 0.1);
}

.custom-area-1:hover, .custom-area-2:hover {
    border-color: rgba(0, 255, 136, 0.6);
    box-shadow: 0 8px 25px rgba(0, 255, 136, 0.2);
    transform: translateY(-1px);
}

.custom-area-title {
    color: #00ff88;
    font-weight: 600;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 1em;
    padding-bottom: 8px;
    border-bottom: 1px solid rgba(0, 255, 136, 0.3);
    position: relative;
}

.custom-area-title::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 30px;
    height: 1px;
    background: linear-gradient(90deg, #00ff88 0%, #00c6ff 100%);
    border-radius: 1px;
}

.area-hint {
    font-size: 0.75em;
    color: rgba(0, 255, 136, 0.6);
    font-weight: normal;
    margin-left: auto;
}

.custom-content-area {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 15px;
    position: relative;
}

.placeholder-content {
    text-align: center;
    color: rgba(255, 255, 255, 0.8);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    width: 100%;
    height: 100%;
    justify-content: center;
}

.placeholder-icon {
    font-size: 2.5em;
    opacity: 0.7;
    transition: all 0.3s ease;
}

.custom-content-area:hover .placeholder-icon {
    transform: scale(1.05);
    opacity: 0.9;
}

.placeholder-text h3 {
    color: #00ff88;
    margin: 0 0 8px 0;
    font-size: 1.1em;
    font-weight: 600;
}

.placeholder-text p {
    margin: 0;
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.85em;
    line-height: 1.4;
}

/* 添加科技感的装饰元素 */










/* 实时数据监控 */
.data-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.data-item {
    text-align: center;
    padding: 15px;
    background: rgba(0, 150, 255, 0.1);
    border: 1px solid rgba(0, 150, 255, 0.3);
    border-radius: 8px;
}

.data-label {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.8em;
    margin-bottom: 8px;
}

.data-value {
    color: #00ff88;
    font-size: 1.4em;
    font-weight: bold;
    text-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
}

/* 地图显示 */
.map-display {
    height: 300px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.china-map {
    width: 100%;
    height: 100%;
}

.map-svg {
    width: 100%;
    height: 100%;
}

.power-station {
    filter: drop-shadow(0 0 5px #ff6b35);
}

/* 设备在线率 */
.status-bars {
    display: flex;
    justify-content: space-around;
    align-items: end;
    height: 200px;
    padding: 10px 0;
}

.status-bar-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.bar-label {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.8em;
}

.bar-container {
    width: 25px;
    height: 120px;
    background: rgba(0, 150, 255, 0.2);
    border-radius: 4px;
    overflow: hidden;
    position: relative;
}

.bar-fill {
    background: linear-gradient(180deg, #00ff88 0%, #00c6ff 100%);
    width: 100%;
    position: absolute;
    bottom: 0;
    border-radius: 4px;
    box-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
    animation: fillUp 2s ease-out;
}

.bar-value {
    color: #00ff88;
    font-weight: bold;
    font-size: 0.9em;
}

@keyframes fillUp {
    from { height: 0; }
    to { height: var(--height); }
}

/* 故障统计图表 */
.fault-chart {
    height: 150px;
}

.chart-container {
    height: 100%;
    display: flex;
    align-items: end;
    justify-content: center;
}

.bar-chart {
    display: flex;
    align-items: end;
    gap: 8px;
    height: 120px;
}

.chart-bar {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

.bar {
    width: 15px;
    background: linear-gradient(180deg, #ff6b35 0%, #f7971e 100%);
    border-radius: 2px;
    min-height: 10px;
    box-shadow: 0 0 8px rgba(255, 107, 53, 0.5);
    transition: height 0.5s ease;
}

.bar-label {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.7em;
    writing-mode: vertical-rl;
}

/* 产能结构饼图 */
.pie-chart-container {
    display: flex;
    align-items: center;
    gap: 20px;
}

.pie-chart {
    width: 120px;
    height: 120px;
}

.pie-svg {
    width: 100%;
    height: 100%;
    filter: drop-shadow(0 0 10px rgba(0, 150, 255, 0.3));
}

.pie-legend {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.8em;
}

.legend-color {
    width: 12px;
    height: 12px;
    border-radius: 2px;
}

/* 数据统计表格 */
.stats-table {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.8em;
}

.table-header {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    gap: 10px;
    padding: 8px 0;
    border-bottom: 1px solid rgba(0, 150, 255, 0.3);
    color: #00c6ff;
    font-weight: 600;
}

.table-row {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    gap: 10px;
    padding: 8px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* 电能效率 */
.efficiency-bars {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.efficiency-item {
    display: flex;
    align-items: center;
    gap: 10px;
}

.efficiency-label {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.8em;
    min-width: 40px;
}

.efficiency-bar {
    flex: 1;
    height: 8px;
    background: rgba(0, 150, 255, 0.2);
    border-radius: 4px;
    overflow: hidden;
}

.efficiency-fill {
    height: 100%;
    border-radius: 4px;
    transition: width 1s ease;
    box-shadow: 0 0 8px rgba(0, 150, 255, 0.5);
}

.efficiency-value {
    color: #00ff88;
    font-weight: bold;
    font-size: 0.8em;
    min-width: 30px;
}

/* 趋势图表 */
.trend-chart {
    height: 180px;
}

.trend-svg {
    width: 100%;
    height: 140px;
}

.trend-legend {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 10px;
}

.trend-legend-item {
    display: flex;
    align-items: center;
    gap: 5px;
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.8em;
}

.legend-line {
    width: 20px;
    height: 3px;
    border-radius: 2px;
}

/* 加载状态 */
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100vh;
    background: linear-gradient(135deg, #0f1419 0%, #1a2332 50%, #0f1419 100%);
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(0, 150, 255, 0.3);
    border-radius: 50%;
    border-top-color: #00c6ff;
    animation: spin 1s ease-in-out infinite;
    margin-bottom: 20px;
    box-shadow: 0 0 20px rgba(0, 198, 255, 0.3);
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.loading-container p {
    color: #00c6ff;
    font-size: 1.2em;
    font-weight: 500;
    text-shadow: 0 0 10px rgba(0, 198, 255, 0.5);
}

/* 响应式设计 */
@media (max-width: 1400px) {
    .dashboard-grid {
        grid-template-columns: 1fr 2fr 1fr;
        grid-template-rows: 1fr 1fr 0.7fr;
        gap: 15px;
        padding: 6px;
        grid-template-areas: 
            "realtime map devicestatus"
            "trend trend trend"
            "custom1 custom2 custom3";
    }
    
    .module-tabs {
        flex-wrap: wrap;
    }
    
    .tab-name {
        display: none;
    }
    
    .placeholder-text h3 {
        font-size: 1.1em;
    }
    
    .placeholder-text p {
        font-size: 0.85em;
    }
    
    .placeholder-icon {
        font-size: 2.5em;
    }
    
    .custom-area-1, .custom-area-2 {
        min-height: 160px;
    }
}

@media (max-width: 1024px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
        grid-template-rows: auto auto auto auto auto auto auto;
        gap: 12px;
        padding: 4px;
        grid-template-areas: 
            "map"
            "realtime"
            "devicestatus"
            "trend"
            "custom1"
            "custom2"
            "custom3";
        min-height: 1100px;
    }
    
    .power-trend {
        grid-column: span 1;
    }
    
    .top-navigation {
        flex-direction: column;
        gap: 10px;
        padding: 10px;
    }
    
    .nav-center {
        order: 3;
    }
    
    .status-bar {
        flex-wrap: wrap;
        gap: 10px;
    }
    
    .dashboard-card {
        padding: 12px;
        border-radius: 12px;
    }
    
    .custom-area-1, .custom-area-2 {
        min-height: 140px;
        border-radius: 14px;
    }
    
    .placeholder-content {
        flex-direction: row;
        text-align: left;
        gap: 12px;
        padding: 12px;
    }
    
    .placeholder-icon {
        font-size: 2em;
        flex-shrink: 0;
    }
    
    .placeholder-text {
        flex: 1;
    }
    
    .placeholder-text h3 {
        font-size: 1em;
        margin-bottom: 6px;
    }
    
    .placeholder-text p {
        font-size: 0.8em;
        line-height: 1.4;
    }
}

/* 地图容器美化样式 */
.map-container-enhanced {
    position: relative;
    height: 100%;
    min-height: 250px;
    background: linear-gradient(135deg, #1a2332 60%, #232a3b 100%);
    border-radius: 24px;
    box-shadow: 0 0 40px 0 rgba(0,212,255,0.18);
    border: 2.5px solid #00d4ff;
    overflow: hidden;
    transition: box-shadow 0.4s, border-color 0.4s;
    z-index: 2;
    margin: 0;
    cursor: pointer;
    animation: mapGlow 2.5s infinite alternate;
}

.map-container-enhanced:hover {
    box-shadow: 0 0 80px 0 #00d4ff, 0 0 40px 0 #00d4ff55;
    border-color: #00f6ff;
}

/* 地图标题样式 */
.map-title-enhanced {
    font-size: 1.2em;
    color: #00d4ff;
    letter-spacing: 2px;
    margin-bottom: 6px;
    font-weight: 700;
    text-shadow: 0 0 16px #00d4ff99;
    z-index: 3;
    position: relative;
    text-align: center;
}

.map-title-icon {
    font-size: 1.5em;
    vertical-align: middle;
}

/* 地图标题下划线 */
.map-title-underline {
    width: 100px;
    height: 2px;
    margin: 6px auto 0;
    border-radius: 2px;
    background: linear-gradient(90deg, #00d4ff 0%, #00ff88 100%);
    box-shadow: 0 0 12px #00d4ff99;
}

/* 地图内容区域 */
.map-content-area {
    width: 100%;
    height: calc(100% - 50px);
    flex: 1;
    min-height: 200px;
}

/* 设备在线率卡片样式 */
.device-status-card {
    height: 100%;
    width: 100%;
    min-width: 250px;
    background: linear-gradient(135deg, rgba(15, 20, 25, 0.9) 0%, rgba(26, 35, 50, 0.9) 100%);
    border: 2px solid rgba(0, 150, 255, 0.4);
    border-radius: 16px;
    padding: 12px;
    box-shadow: 0 4px 20px rgba(0, 150, 255, 0.15);
    transition: all 0.3s ease;
    flex: 1;
}

/* 设备在线率标题 */
.device-status-title {
    font-size: 1em;
    color: #00c6ff;
    font-weight: 600;
    margin-bottom: 10px;
    text-shadow: 0 0 8px rgba(0, 198, 255, 0.5);
}

.device-status-icon {
    font-size: 1.2em;
    margin-right: 8px;
}

/* 设备在线率内容区域 */
.device-status-content {
    width: 100%;
    height: calc(100% - 35px);
}

.click-hint {
    font-size: 0.7em;
    color: rgba(0, 212, 255, 0.7);
    margin-left: 10px;
    animation: pulse 2s ease-in-out infinite;
    font-weight: normal;
}

/* 控制按钮组 */
.control-buttons {
    display: flex;
    align-items: center;
    margin-left: auto;
}

.refresh-btn, .debug-btn {
    transition: all 0.3s ease;
    border: none;
    outline: none;
    font-family: inherit;
}

.refresh-btn:hover:not(:disabled) {
    background: rgba(0, 255, 100, 0.4) !important;
    transform: scale(1.05);
}

.debug-btn:hover {
    background: rgba(0, 212, 255, 0.4) !important;
    transform: scale(1.05);
}

/* 数据状态显示 */
.data-status {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    margin-bottom: 10px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    border-left: 3px solid;
}

.status-indicator {
    font-size: 0.8em;
    font-weight: 500;
}

.status-indicator.loading {
    color: #00d4ff;
    border-left-color: #00d4ff;
}

.status-indicator.error {
    color: #ff6b6b;
    border-left-color: #ff6b6b;
}

.status-indicator.success {
    color: #00ff64;
    border-left-color: #00ff64;
}

.last-updated {
    font-size: 0.7em;
    color: rgba(255, 255, 255, 0.6);
}

/* 设备状态卡片标题优化 */
.device-status-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
    padding-bottom: 8px;
    border-bottom: 1px solid rgba(0, 212, 255, 0.3);
}

/* 产能结构卡片样式 */
.production-structure-card {
    height: 100%;
    width: 100%;
    min-width: 250px;
    background: linear-gradient(135deg, rgba(15, 20, 25, 0.9) 0%, rgba(26, 35, 50, 0.9) 100%);
    border: 2px solid rgba(0, 255, 136, 0.4);
    border-radius: 16px;
    padding: 12px;
    box-shadow: 0 4px 20px rgba(0, 255, 136, 0.15);
    transition: all 0.3s ease;
    flex: 1;
}

/* 产能结构标题 */
.production-structure-title {
    font-size: 1em;
    color: #00ff88;
    font-weight: 600;
    margin-bottom: 10px;
    text-shadow: 0 0 8px rgba(0, 255, 136, 0.5);
}

.production-structure-icon {
    font-size: 1.2em;
    margin-right: 8px;
}

/* 产能结构内容区域 */
.production-structure-content {
    width: 100%;
    height: calc(100% - 35px);
}

/* 地图发光动画 */
@keyframes mapGlow {
    0% {
        box-shadow: 0 0 40px 0 rgba(0,212,255,0.18);
    }
    100% {
        box-shadow: 0 0 50px 0 rgba(0,212,255,0.25);
    }
}

/* 子组件通用容器样式 */
.module-container {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background: url('/img/index-bg.png') center center / cover no-repeat,
                linear-gradient(135deg, #0f1419 0%, #1a2332 50%, #0f1419 100%);
    padding: 20px;
    box-sizing: border-box;
}

.module-header {
    background: linear-gradient(135deg, rgba(0, 150, 255, 0.1) 0%, rgba(0, 200, 255, 0.1) 100%);
    border: 2px solid rgba(0, 150, 255, 0.3);
    border-radius: 10px;
    padding: 15px 20px;
    margin-bottom: 20px;
    box-shadow: 0 0 20px rgba(0, 150, 255, 0.2);
    flex-shrink: 0;
}

.module-title {
    color: #ffffff;
    font-size: 1.8em;
    margin: 0;
    text-align: center;
    background: linear-gradient(135deg, #00c6ff 0%, #0072ff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 30px rgba(0, 150, 255, 0.5);
}

.module-content {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.module-grid {
    display: grid;
    gap: 20px;
    height: 100%;
    overflow: hidden;
}

/* 2x3网格布局 */
.module-grid-2x3 {
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr 1fr;
}

/* 3x2网格布局 */
.module-grid-3x2 {
    grid-template-columns: 1fr 1fr 1fr;
    grid-template-rows: 1fr 1fr;
}

/* 卡片样式 */
.module-card {
    background: linear-gradient(135deg, rgba(15, 20, 25, 0.8) 0%, rgba(26, 35, 50, 0.8) 100%);
    border: 2px solid rgba(0, 150, 255, 0.3);
    border-radius: 10px;
    padding: 15px;
    backdrop-filter: blur(10px);
    box-shadow: 0 0 20px rgba(0, 150, 255, 0.1);
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.module-card-title {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #00c6ff;
    font-weight: 600;
    font-size: 1em;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 1px solid rgba(0, 150, 255, 0.3);
    flex-shrink: 0;
}

.module-card-content {
    flex: 1;
    overflow: hidden;
}

/* 统计卡片 */
.stats-card {
    background: linear-gradient(135deg, rgba(0, 150, 255, 0.1) 0%, rgba(0, 200, 255, 0.1) 100%);
    border: 1px solid rgba(0, 150, 255, 0.3);
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    color: white;
}

.stats-value {
    font-size: 2em;
    font-weight: bold;
    color: #00ff88;
    text-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
    margin-bottom: 5px;
}

.stats-label {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9em;
}

/* 响应式适配 */
@media (max-width: 1400px) {
    .module-grid-3x2 {
        grid-template-columns: 1fr 1fr;
        grid-template-rows: 1fr 1fr 1fr;
    }
}

@media (max-width: 1024px) {
    .module-grid, .module-grid-2x3, .module-grid-3x2 {
        grid-template-columns: 1fr;
        grid-template-rows: repeat(6, 1fr);
    }
    
    .module-container {
        padding: 15px;
    }
}

/* 电量趋势卡片 */
.power-trend {
    grid-area: trend;
    background: linear-gradient(135deg, rgba(15, 20, 25, 0.9) 0%, rgba(26, 35, 50, 0.9) 100%);
    border: 2px solid rgba(244, 233, 37, 0.4);
    border-radius: 16px;
    padding: 12px;
    box-shadow: 0 4px 20px rgba(244, 233, 37, 0.15);
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
}

.power-trend .card-title {
    color: #f4e925;
    font-size: 1em;
    font-weight: 600;
    margin-bottom: 10px;
    text-shadow: 0 0 8px rgba(244, 233, 37, 0.5);
}

.power-trend-content {
    width: 100%;
    height: calc(100% - 35px);
    flex: 1;
}

/* ==================== 设备详情弹窗样式 ==================== */

/* 弹窗遮罩层 */
.device-detail-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(8px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* 弹窗主体 */
.device-detail-modal {
    background: linear-gradient(135deg, rgba(15, 20, 25, 0.95) 0%, rgba(26, 35, 50, 0.95) 100%);
    border: 2px solid rgba(0, 212, 255, 0.5);
    border-radius: 20px;
    width: 90vw;
    max-width: 1400px;
    height: 85vh;
    max-height: 800px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: relative;
    box-shadow: 
        0 0 50px rgba(0, 212, 255, 0.3),
        inset 0 0 30px rgba(0, 212, 255, 0.1);
    animation: modalSlideIn 0.4s ease-out;
}

@keyframes modalSlideIn {
    from { 
        transform: scale(0.9) translateY(-20px);
        opacity: 0;
    }
    to { 
        transform: scale(1) translateY(0);
        opacity: 1;
    }
}

/* 弹窗头部 */
.modal-header {
    background: linear-gradient(135deg, rgba(0, 212, 255, 0.15) 0%, rgba(0, 150, 255, 0.15) 100%);
    padding: 20px 30px;
    border-bottom: 2px solid rgba(0, 212, 255, 0.3);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;
}

.modal-title {
    display: flex;
    align-items: center;
    gap: 15px;
}

.modal-title h2 {
    color: #ffffff;
    font-size: 1.6em;
    font-weight: 600;
    margin: 0;
    background: linear-gradient(135deg, #00d4ff 0%, #ffffff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.status-icon {
    font-size: 1.5em;
    animation: pulse 2s ease-in-out infinite;
}

.device-count {
    background: rgba(0, 212, 255, 0.2);
    color: #00d4ff;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.9em;
    border: 1px solid rgba(0, 212, 255, 0.5);
}

.close-btn {
    background: rgba(255, 107, 107, 0.2);
    border: 2px solid rgba(255, 107, 107, 0.5);
    border-radius: 50%;
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #ff6b6b;
    font-size: 1.2em;
    font-weight: bold;
}

.close-btn:hover {
    background: rgba(255, 107, 107, 0.4);
    transform: rotate(90deg);
    box-shadow: 0 0 20px rgba(255, 107, 107, 0.5);
}

/* 搜索栏 */
.modal-search {
    padding: 20px 30px;
    background: rgba(0, 212, 255, 0.05);
    border-bottom: 1px solid rgba(0, 212, 255, 0.2);
    position: relative;
    flex-shrink: 0;
}

.search-input {
    width: 100%;
    padding: 12px 45px 12px 15px;
    background: rgba(15, 20, 25, 0.8);
    border: 2px solid rgba(0, 212, 255, 0.3);
    border-radius: 25px;
    color: #ffffff;
    font-size: 1em;
    outline: none;
    transition: all 0.3s ease;
}

.search-input:focus {
    border-color: #00d4ff;
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
}

.search-input::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.search-icon {
    position: absolute;
    right: 45px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 1.2em;
    color: rgba(255, 255, 255, 0.6);
}

/* 弹窗内容区域 */
.modal-content {
    flex: 1;
    overflow-y: auto;
    padding: 20px 30px;
    position: relative;
}

/* 加载状态 */
.loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 300px;
    color: #00d4ff;
}

.loading-state .loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(0, 212, 255, 0.3);
    border-top: 4px solid #00d4ff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

/* 空状态 */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 300px;
    color: rgba(255, 255, 255, 0.6);
}

.empty-icon {
    font-size: 3em;
    margin-bottom: 15px;
    opacity: 0.7;
}

/* 设备网格 */
.device-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 20px;
    padding: 10px 0;
}

/* 设备卡片 */
.device-card {
    background: linear-gradient(135deg, rgba(15, 20, 25, 0.9) 0%, rgba(26, 35, 50, 0.9) 100%);
    border: 2px solid rgba(0, 212, 255, 0.3);
    border-left: 5px solid;
    border-radius: 15px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.device-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, transparent 0%, rgba(0, 212, 255, 0.05) 50%, transparent 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.device-card:hover {
    transform: translateY(-5px);
    border-color: #00d4ff;
    box-shadow: 0 10px 30px rgba(0, 212, 255, 0.3);
}

.device-card:hover::before {
    opacity: 1;
}

.device-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
}

.device-name {
    color: #ffffff;
    font-size: 1.2em;
    font-weight: 600;
    margin: 0;
}

.device-type {
    background: rgba(0, 212, 255, 0.2);
    color: #00d4ff;
    padding: 4px 10px;
    border-radius: 15px;
    font-size: 0.8em;
    border: 1px solid rgba(0, 212, 255, 0.5);
}

.device-card-content {
    margin-bottom: 15px;
}

.device-info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
}

.info-label {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9em;
}

.info-value {
    color: #00d4ff;
    font-weight: 600;
    font-size: 0.9em;
}

.device-card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 15px;
    border-top: 1px solid rgba(0, 212, 255, 0.2);
}

.last-update {
    color: rgba(255, 255, 255, 0.5);
    font-size: 0.8em;
}

.view-details {
    color: #00d4ff;
    font-size: 0.8em;
    font-weight: 500;
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.device-card:hover .view-details {
    opacity: 1;
}

/* 设备详细信息侧边栏 */
.device-detail-sidebar {
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    width: 450px;
    background: linear-gradient(135deg, rgba(15, 20, 25, 0.98) 0%, rgba(26, 35, 50, 0.98) 100%);
    border-left: 2px solid rgba(0, 212, 255, 0.5);
    backdrop-filter: blur(10px);
    display: flex;
    flex-direction: column;
    animation: slideInRight 0.3s ease-out;
    z-index: 10;
}

@keyframes slideInRight {
    from { transform: translateX(100%); }
    to { transform: translateX(0); }
}

.sidebar-header {
    padding: 20px;
    background: rgba(0, 212, 255, 0.1);
    border-bottom: 2px solid rgba(0, 212, 255, 0.3);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;
}

.sidebar-header h3 {
    color: #ffffff;
    font-size: 1.3em;
    font-weight: 600;
    margin: 0;
}

.close-sidebar-btn {
    background: rgba(255, 107, 107, 0.2);
    border: 1px solid rgba(255, 107, 107, 0.5);
    border-radius: 50%;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #ff6b6b;
    font-size: 1em;
}

.close-sidebar-btn:hover {
    background: rgba(255, 107, 107, 0.4);
    transform: rotate(90deg);
}

.sidebar-content {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
}

/* 详情部分 */
.detail-section {
    margin-bottom: 25px;
    padding: 15px;
    background: rgba(0, 212, 255, 0.05);
    border: 1px solid rgba(0, 212, 255, 0.2);
    border-radius: 10px;
}

.detail-section h4 {
    color: #00d4ff;
    font-size: 1.1em;
    font-weight: 600;
    margin: 0 0 15px 0;
    padding-bottom: 8px;
    border-bottom: 1px solid rgba(0, 212, 255, 0.3);
}

.detail-grid {
    display: grid;
    gap: 12px;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
}

.detail-label {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9em;
    font-weight: 500;
}

.detail-value {
    color: #ffffff;
    font-weight: 600;
    font-size: 0.9em;
    text-align: right;
}

.detail-value.highlight {
    color: #00ff64;
    font-weight: 700;
    text-shadow: 0 0 5px rgba(0, 255, 100, 0.5);
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .device-detail-sidebar {
        width: 400px;
    }
    
    .device-grid {
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    }
}

@media (max-width: 900px) {
    .device-detail-modal {
        width: 95vw;
        height: 90vh;
    }
    
    .device-detail-sidebar {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        width: 100%;
        z-index: 20;
    }
    
    .device-grid {
        grid-template-columns: 1fr;
    }
    
    .modal-content {
        padding: 15px 20px;
    }
    
    .modal-search {
        padding: 15px 20px;
    }
    
    .modal-header {
        padding: 15px 20px;
    }
    
    .modal-title h2 {
        font-size: 1.3em;
    }
}

@media (max-width: 600px) {
    .device-info-grid {
        grid-template-columns: 1fr;
    }
    
    .device-card-footer {
        flex-direction: column;
        gap: 8px;
        align-items: flex-start;
    }
}

/* 告警信息展示样式 */
.alert-display-card {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.alert-display-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid rgba(255, 107, 107, 0.3);
}

.alert-title-left {
    display: flex;
    align-items: center;
    gap: 12px;
}

.auto-scroll-btn {
    padding: 4px 8px;
    font-size: 0.7em;
    border: 1px solid rgba(0, 212, 255, 0.4);
    border-radius: 4px;
    background: rgba(0, 212, 255, 0.1);
    color: #00d4ff;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 4px;
    white-space: nowrap;
}

.auto-scroll-btn:hover {
    background: rgba(0, 212, 255, 0.2);
    border-color: rgba(0, 212, 255, 0.6);
    transform: scale(1.05);
}

.auto-scroll-btn.active {
    background: rgba(0, 255, 100, 0.1);
    border-color: rgba(0, 255, 100, 0.4);
    color: #00ff64;
}

.auto-scroll-btn.active:hover {
    background: rgba(0, 255, 100, 0.2);
    border-color: rgba(0, 255, 100, 0.6);
}

.alert-status {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 4px;
    font-size: 0.75em;
}

.alert-count {
    display: flex;
    gap: 12px;
}

.alert-count .critical {
    color: #ff6b6b;
    font-weight: 600;
}

.alert-count .warning {
    color: #f4e925;
    font-weight: 600;
}

.alert-count .info {
    color: #00d4ff;
    font-weight: 600;
}

.last-update {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.9em;
}

.alert-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.alert-table-container {
    flex: 1;
    overflow-y: auto;
    border-radius: 8px;
    border: 1px solid rgba(0, 212, 255, 0.2);
    background: rgba(0, 0, 0, 0.2);
    position: relative;
    transition: border-color 0.3s ease;
}

.alert-table-container:hover {
    border-color: rgba(0, 212, 255, 0.4);
}

.alert-table-container::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 3px;
    height: 100%;
    background: linear-gradient(180deg, 
        rgba(0, 212, 255, 0.8) 0%, 
        rgba(0, 212, 255, 0.3) 50%, 
        rgba(0, 212, 255, 0.8) 100%);
    opacity: 0;
    animation: scrollIndicator 3s linear infinite;
    pointer-events: none;
}

@keyframes scrollIndicator {
    0% {
        opacity: 0;
        transform: translateY(-100%);
    }
    50% {
        opacity: 1;
        transform: translateY(0);
    }
    100% {
        opacity: 0;
        transform: translateY(100%);
    }
}

.alert-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.85em;
}

.alert-table thead {
    background: linear-gradient(135deg, rgba(0, 212, 255, 0.2) 0%, rgba(0, 150, 255, 0.3) 100%);
    position: sticky;
    top: 0;
    z-index: 10;
}

.alert-table th {
    padding: 10px 8px;
    text-align: left;
    color: #00d4ff;
    font-weight: 600;
    font-size: 0.9em;
    border-bottom: 2px solid rgba(0, 212, 255, 0.4);
    white-space: nowrap;
}

.alert-table th:first-child { width: 80px; }
.alert-table th:nth-child(2) { width: 90px; }
.alert-table th:nth-child(3) { width: 130px; }
.alert-table th:nth-child(4) { width: auto; }
.alert-table th:last-child { width: 80px; }

.alert-table-body {
    max-height: 300px;
}

.alert-row {
    transition: all 0.3s ease;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    animation: slideInFromRight 0.5s ease-out;
}

.alert-row:hover {
    background: rgba(0, 212, 255, 0.1);
    transform: translateX(2px);
}

.alert-row.new-alert {
    animation: newAlertPulse 3s ease-out;
    box-shadow: 0 0 15px rgba(255, 107, 107, 0.5);
}

.alert-row td {
    padding: 8px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    vertical-align: middle;
}

.alert-time {
    color: rgba(255, 255, 255, 0.8);
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
}

.alert-level {
    text-align: center;
}

.level-badge {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 0.8em;
    font-weight: 500;
    white-space: nowrap;
}

.level-badge.level-critical {
    background: rgba(255, 107, 107, 0.2);
    color: #ff6b6b;
    border: 1px solid rgba(255, 107, 107, 0.4);
}

.level-badge.level-warning {
    background: rgba(244, 233, 37, 0.2);
    color: #f4e925;
    border: 1px solid rgba(244, 233, 37, 0.4);
}

.level-badge.level-info {
    background: rgba(0, 212, 255, 0.2);
    color: #00d4ff;
    border: 1px solid rgba(0, 212, 255, 0.4);
}

.alert-device {
    color: #00ff88;
    font-weight: 500;
    font-size: 0.9em;
}

.alert-message {
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.3;
    font-size: 0.9em;
}

.alert-status {
    text-align: center;
}

.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 3px;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 0.75em;
    font-weight: 500;
}

.status-badge.status-pending {
    background: rgba(244, 233, 37, 0.2);
    color: #f4e925;
    border: 1px solid rgba(244, 233, 37, 0.3);
}

.status-badge.status-processing {
    background: rgba(0, 212, 255, 0.2);
    color: #00d4ff;
    border: 1px solid rgba(0, 212, 255, 0.3);
}

.status-badge.status-resolved {
    background: rgba(0, 255, 136, 0.2);
    color: #00ff88;
    border: 1px solid rgba(0, 255, 136, 0.3);
}

.no-alerts {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.9em;
}

.no-alerts-icon {
    font-size: 3em;
    margin-bottom: 10px;
    opacity: 0.7;
}

/* 滚动条样式 */
.alert-table-container::-webkit-scrollbar {
    width: 6px;
}

.alert-table-container::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 3px;
}

.alert-table-container::-webkit-scrollbar-thumb {
    background: rgba(0, 212, 255, 0.5);
    border-radius: 3px;
}

.alert-table-container::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 212, 255, 0.7);
}

/* 动画效果 */
@keyframes slideInFromRight {
    0% {
        opacity: 0;
        transform: translateX(20px);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes newAlertPulse {
    0% {
        background: rgba(255, 107, 107, 0.3);
        box-shadow: 0 0 20px rgba(255, 107, 107, 0.6);
    }
    50% {
        background: rgba(255, 107, 107, 0.1);
        box-shadow: 0 0 10px rgba(255, 107, 107, 0.3);
    }
    100% {
        background: transparent;
        box-shadow: none;
    }
}

/* 响应式适配 */
@media (max-width: 1400px) {
    .alert-table {
        font-size: 0.8em;
    }
    
    .alert-table th,
    .alert-table td {
        padding: 6px 4px;
    }
    
    .alert-status {
        font-size: 0.7em;
    }
    
    .alert-count {
        gap: 8px;
    }
}

@media (max-width: 1024px) {
    .alert-display-title {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .alert-title-left {
        width: 100%;
        justify-content: space-between;
    }
    
    .auto-scroll-btn {
        font-size: 0.65em;
        padding: 3px 6px;
    }
    
    .alert-status {
        align-items: flex-start;
        width: 100%;
    }
    
    .alert-count {
        flex-direction: column;
        gap: 4px;
    }
    
    .alert-table th:nth-child(3),
    .alert-table td:nth-child(3) {
        display: none; /* 隐藏设备列 */
    }
    
    .alert-table th:first-child { width: 70px; }
    .alert-table th:nth-child(2) { width: 80px; }
    .alert-table th:last-child { width: 70px; }
}

/* 交易历史样式 */
.trading-history-card {
    position: relative;
}

.trading-history-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.trading-title-left {
    display: flex;
    align-items: center;
    gap: 8px;
}

.trading-status {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 4px;
}

.trading-count {
    display: flex;
    gap: 12px;
    font-size: 12px;
}

.buy-count {
    color: #ff6b6b;
}

.sell-count {
    color: #00ff88;
}

.service-count {
    color: #f4e925;
}

.trading-summary {
    font-size: 12px;
    color: #00d4ff;
    font-weight: bold;
}

.trading-content {
    height: calc(100% - 50px);
    position: relative;
}

.trading-table-container {
    height: 100%;
    overflow-y: auto;
    border: 1px solid rgba(0, 212, 255, 0.3);
    border-radius: 8px;
    background: rgba(0, 0, 0, 0.3);
    position: relative;
}

.trading-table-container::-webkit-scrollbar {
    width: 4px;
}

.trading-table-container::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 2px;
}

.trading-table-container::-webkit-scrollbar-thumb {
    background: linear-gradient(to bottom, #00d4ff, #0099cc);
    border-radius: 2px;
}

.trading-table-container::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(to bottom, #00ff88, #00cc6a);
}

.trading-table {
    width: 100%;
    border-collapse: collapse;
    color: #ffffff;
    font-size: 11px;
}

.trading-table thead {
    position: sticky;
    top: 0;
    background: linear-gradient(135deg, rgba(0, 212, 255, 0.2), rgba(0, 255, 136, 0.1));
    z-index: 10;
}

.trading-table th {
    padding: 8px 6px;
    text-align: left;
    border-bottom: 1px solid rgba(0, 212, 255, 0.3);
    color: #00d4ff;
    font-weight: bold;
    font-size: 10px;
    white-space: nowrap;
}

.trading-table td {
    padding: 6px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    vertical-align: middle;
}

.trading-row {
    transition: all 0.3s ease;
    animation: slideInFromTop 0.5s ease-out;
}

.trading-row:hover {
    background: rgba(0, 212, 255, 0.1);
    border-left: 3px solid #00d4ff;
}

.trading-row.new-trading {
    animation: 
        slideInFromTop 0.5s ease-out,
        flashHighlight 3s ease-out;
    background: rgba(255, 215, 0, 0.2);
    border-left: 3px solid #ffd700;
}

@keyframes slideInFromTop {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes flashHighlight {
    0%, 100% {
        background: rgba(255, 215, 0, 0);
        border-left-color: transparent;
    }
    25%, 75% {
        background: rgba(255, 215, 0, 0.2);
        border-left-color: #ffd700;
    }
    50% {
        background: rgba(255, 215, 0, 0.3);
        border-left-color: #ffed4e;
    }
}

.trading-time {
    min-width: 60px;
}

.time-container {
    display: flex;
    flex-direction: column;
    gap: 1px;
}

.time-container .date {
    font-size: 9px;
    color: #8cc8ff;
}

.time-container .time {
    font-size: 10px;
    color: #ffffff;
    font-weight: bold;
}

.trading-type {
    min-width: 70px;
}

.type-badge {
    display: inline-block;
    font-size: 10px;
    font-weight: bold;
    padding: 2px 6px;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid currentColor;
    white-space: nowrap;
}

.type-buy {
    background: rgba(255, 107, 107, 0.15);
    border-color: #ff6b6b;
}

.type-sell {
    background: rgba(0, 255, 136, 0.15);
    border-color: #00ff88;
}

.type-peak {
    background: rgba(244, 233, 37, 0.15);
    border-color: #f4e925;
}

.type-frequency {
    background: rgba(0, 212, 255, 0.15);
    border-color: #00d4ff;
}

.trading-counterparty {
    max-width: 90px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 10px;
    color: #e0e0e0;
}

.trading-volume {
    min-width: 60px;
    text-align: right;
}

.volume-value {
    font-weight: bold;
    color: #00ff88;
}

.volume-unit {
    font-size: 8px;
    color: #8cc8ff;
    margin-left: 2px;
}

.trading-price {
    min-width: 70px;
    text-align: right;
}

.price-container {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 1px;
}

.price-value {
    font-weight: bold;
    color: #ffd700;
    font-size: 10px;
}

.price-unit {
    font-size: 8px;
    color: #8cc8ff;
}

.trading-status {
    min-width: 70px;
    text-align: center;
}

.status-badge {
    display: inline-block;
    font-size: 9px;
    padding: 2px 6px;
    border-radius: 10px;
    white-space: nowrap;
}

.status-pending {
    background: rgba(255, 165, 0, 0.2);
    color: #ffa500;
    border: 1px solid #ffa500;
}

.status-completed {
    background: rgba(0, 255, 136, 0.2);
    color: #00ff88;
    border: 1px solid #00ff88;
}

.no-trading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #8cc8ff;
    opacity: 0.7;
}

.no-trading-icon {
    font-size: 36px;
    margin-bottom: 12px;
}

.no-trading p {
    margin: 0;
    font-size: 14px;
}
