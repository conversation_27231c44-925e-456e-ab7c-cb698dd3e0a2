import React, { useRef, useEffect, useState } from 'react';
import * as echarts from 'echarts';
import './style.css';

export default function TradingCenter() {
    // 图表引用
    const pricePredictionRef = useRef(null);
    const costAnalysisRef = useRef(null);
    const bidAnalysisRef = useRef(null);
    const quarterlyReviewRef = useRef(null);
    const monthlyReviewRef = useRef(null);
    const yearlyReviewRef = useRef(null);
    const historyTradeRef = useRef(null);


    // 交易中心统计数据
    const [tradingStats, setTradingStats] = useState({
        totalTransactions: 3256,
        averagePrice: 0.85,
        tradingVolume: 12500,
        profitMargin: 22.4,
        totalPenalty: 15420,
        penaltyRate: 2.8
    });

    // 视图模式选择
    const [viewMode, setViewMode] = useState('overview');
    // 时间周期选择
    const [reviewPeriod, setReviewPeriod] = useState('quarterly');
    // 展开的交易记录
    const [expandedRows, setExpandedRows] = useState(new Set());
    
    // 历史交易数据
    const [historyData, setHistoryData] = useState([
        { 
            id: 1, 
            date: '2024-01-15', 
            type: '日前交易', 
            volume: 1500, 
            price: 0.85, 
            status: '成功', 
            penalty: 0,
            penaltyDetails: []
        },
        { 
            id: 2, 
            date: '2024-01-16', 
            type: '实时交易', 
            volume: 800, 
            price: 0.92, 
            status: '失败', 
            penalty: 2400,
            penaltyDetails: [
                { type: '未履约', amount: 1500, reason: '系统故障导致未能及时响应' },
                { type: '偏差电量', amount: 900, reason: '实际发电量与申报偏差25%' }
            ]
        },
        { 
            id: 3, 
            date: '2024-01-17', 
            type: '日前交易', 
            volume: 2200, 
            price: 0.78, 
            status: '成功', 
            penalty: 0,
            penaltyDetails: []
        },
        { 
            id: 4, 
            date: '2024-01-18', 
            type: '调频交易', 
            volume: 500, 
            price: 1.15, 
            status: '部分成功', 
            penalty: 800,
            penaltyDetails: [
                { type: '响应延迟', amount: 800, reason: '调频响应时间超过规定标准5秒' }
            ]
        },
        { 
            id: 5, 
            date: '2024-01-19', 
            type: '备用交易', 
            volume: 1200, 
            price: 0.65, 
            status: '成功', 
            penalty: 0,
            penaltyDetails: []
        },
        { 
            id: 6, 
            date: '2024-01-20', 
            type: '实时交易', 
            volume: 900, 
            price: 0.88, 
            status: '失败', 
            penalty: 1800,
            penaltyDetails: [
                { type: '通信故障', amount: 1200, reason: '通信系统中断15分钟' },
                { type: '数据延迟', amount: 600, reason: '数据上报延迟超过10分钟' }
            ]
        }
    ]);



    useEffect(() => {
        // 初始化所有图表
        setTimeout(() => {
            if (viewMode === 'overview') {
                initPricePredictionChart();
                initCostAnalysisChart();
                initBidAnalysisChart();
                initQuarterlyReviewChart();
                initMonthlyReviewChart();
                initYearlyReviewChart();
            } else if (viewMode === 'history') {
                initHistoryTradeChart();
            }
        }, 100);

        // 清理函数
        return () => {
            if (pricePredictionRef.current) echarts.dispose(pricePredictionRef.current);
            if (costAnalysisRef.current) echarts.dispose(costAnalysisRef.current);
            if (bidAnalysisRef.current) echarts.dispose(bidAnalysisRef.current);
            if (quarterlyReviewRef.current) echarts.dispose(quarterlyReviewRef.current);
            if (monthlyReviewRef.current) echarts.dispose(monthlyReviewRef.current);
            if (yearlyReviewRef.current) echarts.dispose(yearlyReviewRef.current);
            if (historyTradeRef.current) echarts.dispose(historyTradeRef.current);
        };
    }, [viewMode]);

    // 价格预测图表
    const initPricePredictionChart = () => {
        if (!pricePredictionRef.current) return;
        const chart = echarts.init(pricePredictionRef.current, 'dark');
        
        const option = {
            backgroundColor: 'transparent',
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'cross',
                    label: {
                        backgroundColor: '#6a7985'
                    }
                }
            },
            legend: {
                data: ['历史价格', '预测价格', '预测区间'],
                textStyle: {
                    color: '#fff'
                }
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日', '下周一', '下周二', '下周三'],
                axisLabel: {
                    color: '#fff'
                },
                axisLine: {
                    lineStyle: {
                        color: 'rgba(0, 212, 255, 0.5)'
                    }
                }
            },
            yAxis: {
                type: 'value',
                name: '电价(元/kWh)',
                axisLabel: {
                    color: '#fff',
                    formatter: '{value}'
                },
                axisLine: {
                    lineStyle: {
                        color: 'rgba(0, 212, 255, 0.5)'
                    }
                },
                splitLine: {
                    lineStyle: {
                        color: 'rgba(0, 212, 255, 0.2)'
                    }
                }
            },
            series: [
                {
                    name: '历史价格',
                    type: 'line',
                    data: [0.82, 0.85, 0.88, 0.86, 0.85, 0.82, 0.80],
                    lineStyle: {
                        width: 3,
                        color: '#00d4ff'
                    },
                    symbol: 'circle',
                    symbolSize: 8
                },
                {
                    name: '预测价格',
                    type: 'line',
                    data: [null, null, null, null, null, null, 0.80, 0.83, 0.87, 0.89],
                    lineStyle: {
                        width: 3,
                        color: '#FFD23F',
                        type: 'dashed'
                    },
                    symbol: 'circle',
                    symbolSize: 8
                },
                {
                    name: '预测区间',
                    type: 'line',
                    data: [null, null, null, null, null, null, 0.83, 0.86, 0.90, 0.92],
                    lineStyle: {
                        width: 0
                    },
                    areaStyle: {
                        color: 'rgba(255, 210, 63, 0.2)'
                    },
                    stack: 'confidence-band',
                    symbol: 'none'
                },
                {
                    name: '预测区间',
                    type: 'line',
                    data: [null, null, null, null, null, null, 0.77, 0.80, 0.84, 0.86],
                    lineStyle: {
                        width: 0
                    },
                    areaStyle: {
                        color: 'rgba(255, 210, 63, 0.2)'
                    },
                    stack: 'confidence-band',
                    symbol: 'none'
                }
            ]
        };
        
        chart.setOption(option);
        
        // 响应式调整
        window.addEventListener('resize', () => {
            chart.resize();
        });
    };

    // 成本分析图表
    const initCostAnalysisChart = () => {
        if (!costAnalysisRef.current) return;
        const chart = echarts.init(costAnalysisRef.current, 'dark');
        
        const option = {
            backgroundColor: 'transparent',
            tooltip: {
                trigger: 'item',
                formatter: '{a} <br/>{b}: {c} ({d}%)'
            },
            legend: {
                orient: 'vertical',
                left: 10,
                data: ['发电成本', '储能成本', '运维成本', '交易费用', '其他成本'],
                textStyle: {
                    color: '#fff'
                }
            },
            series: [
                {
                    name: '成本构成',
                    type: 'pie',
                    radius: ['40%', '70%'],
                    center: ['65%', '50%'],
                    avoidLabelOverlap: false,
                    itemStyle: {
                        borderRadius: 10,
                        borderColor: '#121826',
                        borderWidth: 2
                    },
                    label: {
                        show: false,
                        position: 'center'
                    },
                    emphasis: {
                        label: {
                            show: true,
                            fontSize: '18',
                            fontWeight: 'bold',
                            color: '#fff'
                        }
                    },
                    labelLine: {
                        show: false
                    },
                    data: [
                        { value: 45, name: '发电成本', itemStyle: { color: '#FF6B35' } },
                        { value: 25, name: '储能成本', itemStyle: { color: '#F7931E' } },
                        { value: 15, name: '运维成本', itemStyle: { color: '#FFD23F' } },
                        { value: 10, name: '交易费用', itemStyle: { color: '#06FFA5' } },
                        { value: 5, name: '其他成本', itemStyle: { color: '#4D9DE0' } }
                    ]
                }
            ]
        };
        
        chart.setOption(option);
        
        // 响应式调整
        window.addEventListener('resize', () => {
            chart.resize();
        });
    };

    // 报价分析图表
    const initBidAnalysisChart = () => {
        if (!bidAnalysisRef.current) return;
        const chart = echarts.init(bidAnalysisRef.current, 'dark');
        
        const option = {
            backgroundColor: 'transparent',
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                }
            },
            legend: {
                data: ['成功报价', '失败报价', '平均成交价'],
                textStyle: {
                    color: '#fff'
                }
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: ['峰时', '平时', '谷时', '尖峰', '低谷'],
                axisLabel: {
                    color: '#fff'
                },
                axisLine: {
                    lineStyle: {
                        color: 'rgba(0, 212, 255, 0.5)'
                    }
                }
            },
            yAxis: [
                {
                    type: 'value',
                    name: '报价次数',
                    axisLabel: {
                        color: '#fff'
                    },
                    axisLine: {
                        lineStyle: {
                            color: 'rgba(0, 212, 255, 0.5)'
                        }
                    },
                    splitLine: {
                        lineStyle: {
                            color: 'rgba(0, 212, 255, 0.2)'
                        }
                    }
                },
                {
                    type: 'value',
                    name: '价格(元/kWh)',
                    axisLabel: {
                        color: '#fff',
                        formatter: '{value}'
                    },
                    axisLine: {
                        lineStyle: {
                            color: 'rgba(0, 212, 255, 0.5)'
                        }
                    },
                    splitLine: {
                        show: false
                    }
                }
            ],
            series: [
                {
                    name: '成功报价',
                    type: 'bar',
                    stack: 'total',
                    data: [120, 132, 101, 134, 90],
                    itemStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            { offset: 0, color: '#06FFA5' },
                            { offset: 1, color: '#06FFA540' }
                        ])
                    }
                },
                {
                    name: '失败报价',
                    type: 'bar',
                    stack: 'total',
                    data: [20, 32, 21, 34, 10],
                    itemStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            { offset: 0, color: '#FF6B35' },
                            { offset: 1, color: '#FF6B3540' }
                        ])
                    }
                },
                {
                    name: '平均成交价',
                    type: 'line',
                    yAxisIndex: 1,
                    data: [0.92, 0.85, 0.75, 1.05, 0.68],
                    lineStyle: {
                        color: '#FFD23F',
                        width: 3
                    },
                    symbol: 'circle',
                    symbolSize: 8
                }
            ]
        };
        
        chart.setOption(option);
        
        // 响应式调整
        window.addEventListener('resize', () => {
            chart.resize();
        });
    };

    // 季度复盘图表
    const initQuarterlyReviewChart = () => {
        if (!quarterlyReviewRef.current) return;
        const chart = echarts.init(quarterlyReviewRef.current, 'dark');
        
        const option = {
            backgroundColor: 'transparent',
            tooltip: {
                trigger: 'axis'
            },
            legend: {
                data: ['交易量', '交易额', '利润率'],
                textStyle: {
                    color: '#fff'
                }
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: ['第一季度', '第二季度', '第三季度', '第四季度'],
                axisLabel: {
                    color: '#fff'
                },
                axisLine: {
                    lineStyle: {
                        color: 'rgba(0, 212, 255, 0.5)'
                    }
                }
            },
            yAxis: [
                {
                    type: 'value',
                    name: '交易量(MWh)',
                    axisLabel: {
                        color: '#fff'
                    },
                    axisLine: {
                        lineStyle: {
                            color: 'rgba(0, 212, 255, 0.5)'
                        }
                    },
                    splitLine: {
                        lineStyle: {
                            color: 'rgba(0, 212, 255, 0.2)'
                        }
                    }
                },
                {
                    type: 'value',
                    name: '利润率(%)',
                    max: 50,
                    axisLabel: {
                        color: '#fff',
                        formatter: '{value}%'
                    },
                    axisLine: {
                        lineStyle: {
                            color: 'rgba(0, 212, 255, 0.5)'
                        }
                    },
                    splitLine: {
                        show: false
                    }
                }
            ],
            series: [
                {
                    name: '交易量',
                    type: 'bar',
                    data: [3200, 3500, 4100, 3800],
                    itemStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            { offset: 0, color: '#00d4ff' },
                            { offset: 1, color: '#00d4ff40' }
                        ])
                    }
                },
                {
                    name: '交易额',
                    type: 'bar',
                    data: [2800, 3100, 3600, 3300],
                    itemStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            { offset: 0, color: '#4D9DE0' },
                            { offset: 1, color: '#4D9DE040' }
                        ])
                    }
                },
                {
                    name: '利润率',
                    type: 'line',
                    yAxisIndex: 1,
                    data: [20.5, 22.8, 25.3, 23.6],
                    lineStyle: {
                        color: '#FFD23F',
                        width: 3
                    },
                    symbol: 'circle',
                    symbolSize: 8
                }
            ]
        };
        
        chart.setOption(option);
        
        // 响应式调整
        window.addEventListener('resize', () => {
            chart.resize();
        });
    };

    // 月度复盘图表
    const initMonthlyReviewChart = () => {
        if (!monthlyReviewRef.current) return;
        const chart = echarts.init(monthlyReviewRef.current, 'dark');
        
        const option = {
            backgroundColor: 'transparent',
            tooltip: {
                trigger: 'axis',
                backgroundColor: 'rgba(0, 0, 0, 0.8)',
                borderColor: '#FF4500',
                borderWidth: 1,
                textStyle: {
                    color: '#fff'
                }
            },
            animation: true,
            animationDuration: 1500,
            animationEasing: 'elasticOut',
            legend: {
                data: ['交易量', '交易额', '利润率'],
                textStyle: {
                    color: '#fff'
                }
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: ['1月', '2月', '3月', '4月', '5月', '6月'],
                axisLabel: {
                    color: '#fff'
                },
                axisLine: {
                    lineStyle: {
                        color: 'rgba(0, 212, 255, 0.5)'
                    }
                }
            },
            yAxis: [
                {
                    type: 'value',
                    name: '交易量(MWh)',
                    axisLabel: {
                        color: '#fff'
                    },
                    axisLine: {
                        lineStyle: {
                            color: 'rgba(0, 212, 255, 0.5)'
                        }
                    },
                    splitLine: {
                        lineStyle: {
                            color: 'rgba(0, 212, 255, 0.2)'
                        }
                    }
                },
                {
                    type: 'value',
                    name: '利润率(%)',
                    max: 50,
                    axisLabel: {
                        color: '#fff',
                        formatter: '{value}%'
                    },
                    axisLine: {
                        lineStyle: {
                            color: 'rgba(0, 212, 255, 0.5)'
                        }
                    },
                    splitLine: {
                        show: false
                    }
                }
            ],
            series: [
                {
                    name: '交易量',
                    type: 'bar',
                    data: [1050, 1080, 1070, 1150, 1180, 1170],
                    itemStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            { offset: 0, color: '#00d4ff' },
                            { offset: 1, color: '#00d4ff40' }
                        ])
                    }
                },
                {
                    name: '交易额',
                    type: 'bar',
                    data: [920, 950, 930, 1020, 1050, 1030],
                    itemStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            { offset: 0, color: '#4D9DE0' },
                            { offset: 1, color: '#4D9DE040' }
                        ])
                    }
                },
                {
                    name: '利润率',
                    type: 'line',
                    yAxisIndex: 1,
                    data: [19.5, 20.2, 21.8, 22.3, 23.1, 22.8],
                    lineStyle: {
                        color: '#FFD23F',
                        width: 3
                    },
                    symbol: 'circle',
                    symbolSize: 8
                }
            ]
        };
        
        chart.setOption(option);
        
        // 响应式调整
        window.addEventListener('resize', () => {
            chart.resize();
        });
    };

    // 年度复盘图表
    const initYearlyReviewChart = () => {
        if (!yearlyReviewRef.current) return;
        const chart = echarts.init(yearlyReviewRef.current, 'dark');
        
        const option = {
            backgroundColor: 'transparent',
            tooltip: {
                trigger: 'axis'
            },
            legend: {
                data: ['交易量', '交易额', '利润率'],
                textStyle: {
                    color: '#fff'
                }
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: ['2018年', '2019年', '2020年', '2021年', '2022年', '2023年'],
                axisLabel: {
                    color: '#fff'
                },
                axisLine: {
                    lineStyle: {
                        color: 'rgba(0, 212, 255, 0.5)'
                    }
                }
            },
            yAxis: [
                {
                    type: 'value',
                    name: '交易量(MWh)',
                    axisLabel: {
                        color: '#fff'
                    },
                    axisLine: {
                        lineStyle: {
                            color: 'rgba(0, 212, 255, 0.5)'
                        }
                    },
                    splitLine: {
                        lineStyle: {
                            color: 'rgba(0, 212, 255, 0.2)'
                        }
                    }
                },
                {
                    type: 'value',
                    name: '利润率(%)',
                    max: 50,
                    axisLabel: {
                        color: '#fff',
                        formatter: '{value}%'
                    },
                    axisLine: {
                        lineStyle: {
                            color: 'rgba(0, 212, 255, 0.5)'
                        }
                    },
                    splitLine: {
                        show: false
                    }
                }
            ],
            series: [
                {
                    name: '交易量',
                    type: 'bar',
                    data: [8500, 9800, 11200, 12500, 14000, 15500],
                    itemStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            { offset: 0, color: '#00d4ff' },
                            { offset: 1, color: '#00d4ff40' }
                        ])
                    }
                },
                {
                    name: '交易额',
                    type: 'bar',
                    data: [7200, 8500, 9800, 11000, 12500, 13800],
                    itemStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            { offset: 0, color: '#4D9DE0' },
                            { offset: 1, color: '#4D9DE040' }
                        ])
                    }
                },
                {
                    name: '利润率',
                    type: 'line',
                    yAxisIndex: 1,
                    data: [15.2, 17.5, 19.3, 20.8, 22.1, 22.4],
                    lineStyle: {
                        color: '#FFD23F',
                        width: 3
                    },
                    symbol: 'circle',
                    symbolSize: 8
                }
            ]
        };
        
        chart.setOption(option);
        
        // 响应式调整
        window.addEventListener('resize', () => {
            chart.resize();
        });
    };

    // 历史交易图表
    const initHistoryTradeChart = () => {
        if (!historyTradeRef.current) return;
        const chart = echarts.init(historyTradeRef.current, 'dark');
        
        const option = {
            backgroundColor: 'transparent',
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'cross'
                }
            },
            legend: {
                data: ['交易量', '成交价格', '罚款金额'],
                textStyle: {
                    color: '#fff'
                }
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: ['01-15', '01-16', '01-17', '01-18', '01-19', '01-20', '01-21', '01-22'],
                axisLabel: {
                    color: '#fff'
                },
                axisLine: {
                    lineStyle: {
                        color: 'rgba(0, 212, 255, 0.5)'
                    }
                }
            },
            yAxis: [
                {
                    type: 'value',
                    name: '交易量(MWh)',
                    axisLabel: {
                        color: '#fff'
                    },
                    axisLine: {
                        lineStyle: {
                            color: 'rgba(0, 212, 255, 0.5)'
                        }
                    },
                    splitLine: {
                        lineStyle: {
                            color: 'rgba(0, 212, 255, 0.2)'
                        }
                    }
                },
                {
                    type: 'value',
                    name: '金额(元)',
                    axisLabel: {
                        color: '#fff'
                    },
                    axisLine: {
                        lineStyle: {
                            color: 'rgba(0, 212, 255, 0.5)'
                        }
                    },
                    splitLine: {
                        show: false
                    }
                }
            ],
            series: [
                {
                    name: '交易量',
                    type: 'bar',
                    data: [1500, 800, 2200, 500, 1200, 900, 1800, 1600],
                    itemStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            { offset: 0, color: '#00d4ff' },
                            { offset: 1, color: '#00d4ff40' }
                        ])
                    }
                },
                {
                    name: '成交价格',
                    type: 'line',
                    yAxisIndex: 1,
                    data: [0.85, 0.92, 0.78, 1.15, 0.65, 0.88, 0.82, 0.86],
                    lineStyle: {
                        color: '#FFD23F',
                        width: 3
                    },
                    symbol: 'circle',
                    symbolSize: 8
                },
                {
                    name: '罚款金额',
                    type: 'line',
                    yAxisIndex: 1,
                    data: [0, 2400, 0, 800, 0, 1800, 1200, 0],
                    lineStyle: {
                        color: '#FF6B35',
                        width: 3
                    },
                    symbol: 'triangle',
                    symbolSize: 10
                }
            ]
        };
        
        chart.setOption(option);
        
        window.addEventListener('resize', () => {
            chart.resize();
        });
    };



    // 处理视图模式切换
    const handleViewModeChange = (mode) => {
        setViewMode(mode);
    };

    // 处理时间周期选择
    const handlePeriodChange = (period) => {
        setReviewPeriod(period);
    };

    // 切换行展开状态
    const toggleRowExpansion = (id) => {
        const newExpandedRows = new Set(expandedRows);
        if (newExpandedRows.has(id)) {
            newExpandedRows.delete(id);
        } else {
            newExpandedRows.add(id);
        }
        setExpandedRows(newExpandedRows);
    };

    // 渲染交易复盘图表
    const renderReviewChart = () => {
        switch (reviewPeriod) {
            case 'quarterly':
                return (
                    <div className="chart-card" style={{ gridArea: 'review' }}>
                        <div className="card-title">
                            <span className="title-icon">📊</span>
                            季度交易复盘
                        </div>
                        <div className="chart-container" ref={quarterlyReviewRef}></div>
                    </div>
                );
            case 'monthly':
                return (
                    <div className="chart-card" style={{ gridArea: 'review' }}>
                        <div className="card-title">
                            <span className="title-icon">📊</span>
                            月度交易复盘
                        </div>
                        <div className="chart-container" ref={monthlyReviewRef}></div>
                    </div>
                );
            case 'yearly':
                return (
                    <div className="chart-card" style={{ gridArea: 'review' }}>
                        <div className="card-title">
                            <span className="title-icon">📊</span>
                            年度交易复盘
                        </div>
                        <div className="chart-container" ref={yearlyReviewRef}></div>
                    </div>
                );
            default:
                return (
                    <div className="chart-card" style={{ gridArea: 'review' }}>
                        <div className="card-title">
                            <span className="title-icon">📊</span>
                            季度交易复盘
                        </div>
                        <div className="chart-container" ref={quarterlyReviewRef}></div>
                    </div>
                );
        }
    };

    return (
        <div className="trading-center-container">
            {/* 页面标题区域 */}
            <div className="page-header">
                <div className="title-section">
                    <div className="title-icon-wrapper">
                        <span className="title-icon">💰</span>
                    </div>
                    <div className="title-content">
                        <h1 className="page-title">交易中心</h1>
                        <p className="page-subtitle">实时监控交易数据，优化交易策略</p>
                    </div>
                </div>
             
            </div>

            {/* 视图模式选择器 */}
            <div className="view-mode-selector">
                <div className="selector-label">查看模式:</div>
                <div className="selector-options">
                    <button 
                        className={`mode-option ${viewMode === 'overview' ? 'active' : ''}`}
                        onClick={() => handleViewModeChange('overview')}
                    >
                        🔄 交易面板
                    </button>
                    <button 
                        className={`mode-option ${viewMode === 'history' ? 'active' : ''}`}
                        onClick={() => handleViewModeChange('history')}
                    >
                        📊 历史交易
                    </button>
                </div>
            </div>

            {viewMode === 'overview' && (
                <>
                    {/* 时间周期选择器 */}
                    <div className="period-selector">
                        <div className="selector-label">交易复盘周期:</div>
                        <div className="selector-options">
                            <button 
                                className={`period-option ${reviewPeriod === 'quarterly' ? 'active' : ''}`}
                                onClick={() => handlePeriodChange('quarterly')}
                            >
                                季度
                            </button>
                            <button 
                                className={`period-option ${reviewPeriod === 'monthly' ? 'active' : ''}`}
                                onClick={() => handlePeriodChange('monthly')}
                            >
                                月度
                            </button>
                            <button 
                                className={`period-option ${reviewPeriod === 'yearly' ? 'active' : ''}`}
                                onClick={() => handlePeriodChange('yearly')}
                            >
                                年度
                            </button>
                        </div>
                    </div>

                    {/* 图表网格 */}
                    <div className="trading-charts-grid">
                        <div className="chart-card" style={{ gridArea: 'price' }}>
                            <div className="card-title">
                                <span className="title-icon">🔮</span>
                                价格预测
                            </div>
                            <div className="chart-container" ref={pricePredictionRef}></div>
                        </div>

                        <div className="chart-card" style={{ gridArea: 'bid' }}>
                            <div className="card-title">
                                <span className="title-icon">📝</span>
                                报价分析
                            </div>
                            <div className="chart-container" ref={bidAnalysisRef}></div>
                        </div>
                        {renderReviewChart()}
                        <div className="chart-card" style={{ gridArea: 'cost' }}>
                            <div className="card-title">
                                <span className="title-icon">💹</span>
                                成本分析
                            </div>
                            <div className="chart-container" ref={costAnalysisRef}></div>
                        </div>
                    </div>
                </>
            )}

            {viewMode === 'history' && (
                <div className="history-view">
                    <div className="history-charts">
                        <div className="chart-card">
                            <div className="card-title">
                                <span className="title-icon">📈</span>
                                历史交易趋势
                            </div>
                            <div className="chart-container" ref={historyTradeRef}></div>
                        </div>
                    </div>
                    
                    <div className="history-table-card">
                        <div className="card-title">
                            <span className="title-icon">📋</span>
                            交易记录详情
                        </div>
                        <div className="history-table">
                            <table>
                                <thead>
                                    <tr>
                                        <th>操作</th>
                                        <th>日期</th>
                                        <th>交易类型</th>
                                        <th>交易量(MWh)</th>
                                        <th>价格(元/kWh)</th>
                                        <th>状态</th>
                                        <th>罚款(元)</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {historyData.map(item => (
                                        <>
                                            <tr key={item.id} className="main-row">
                                                <td>
                                                    {item.penalty > 0 && (
                                                        <button 
                                                            className="expand-btn"
                                                            onClick={() => toggleRowExpansion(item.id)}
                                                        >
                                                            {expandedRows.has(item.id) ? '▼' : '▶'}
                                                        </button>
                                                    )}
                                                </td>
                                                <td>{item.date}</td>
                                                <td>{item.type}</td>
                                                <td>{item.volume}</td>
                                                <td>{item.price}</td>
                                                <td>
                                                    <span className={`status ${item.status === '成功' ? 'success' : 
                                                        item.status === '失败' ? 'failed' : 'partial'}`}>
                                                        {item.status}
                                                    </span>
                                                </td>
                                                <td className={item.penalty > 0 ? 'penalty-amount' : ''}>
                                                    {item.penalty || '-'}
                                                    {item.penalty > 0 && (
                                                        <span className="penalty-detail-hint">
                                                            {expandedRows.has(item.id) ? ' 收起详情' : ' 查看详情'}
                                                        </span>
                                                    )}
                                                </td>
                                            </tr>
                                            {expandedRows.has(item.id) && item.penaltyDetails.length > 0 && (
                                                <tr key={`${item.id}-details`} className="detail-row">
                                                    <td colSpan="7">
                                                        <div className="penalty-details">
                                                            <div className="penalty-details-header">
                                                                <span className="details-icon">💸</span>
                                                                罚款详情分析
                                                            </div>
                                                            <div className="penalty-details-content">
                                                                {item.penaltyDetails.map((detail, index) => (
                                                                    <div key={index} className="penalty-item">
                                                                        <div className="penalty-item-header">
                                                                            <span className="penalty-type">{detail.type}</span>
                                                                            <span className="penalty-amount-detail">
                                                                                {detail.amount} 元
                                                                            </span>
                                                                        </div>
                                                                        <div className="penalty-reason">
                                                                            原因: {detail.reason}
                                                                        </div>
                                                                    </div>
                                                                ))}
                                                                <div className="penalty-summary">
                                                                    <div className="summary-line">
                                                                        <span>罚款总计:</span>
                                                                        <span className="total-penalty">{item.penalty} 元</span>
                                                                    </div>
                                                                    <div className="summary-line">
                                                                        <span>罚款项目:</span>
                                                                        <span>{item.penaltyDetails.length} 项</span>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </td>
                                                </tr>
                                            )}
                                        </>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            )}


        </div>
    );
}
