* {
    box-sizing: border-box;
}

body {
    margin: 0;
    color: #111;
    font-family: 'sans serif';
    text-decoration: none;
    overflow: hidden;
}

.page {
    height: 100vh;
    width: 100vw;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    background: linear-gradient(135deg, #0f1419 0%, #1a2332 50%, #0f1419 100%);
}

.first-look {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.first-look::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1;
}

.first-look-v {
    text-align: center;
    color: white;
    z-index: 2;
    position: relative;
    max-width: 800px;
    padding: 0 20px;
}

.first-look-slogan {
    font-size: 2.5em;
    font-weight: 700;
    margin-bottom: 20px;
    background: linear-gradient(135deg, #00c6ff 0%, #0072ff 50%, #00ff88 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1.2;
}

.first-look-description {
    font-size: 1.1em;
    line-height: 1.6;
    margin-bottom: 30px;
    opacity: 0.9;
}

.first-look-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
}

.btn-1 {
    padding: 12px 30px;
    background: linear-gradient(135deg, #00c6ff 0%, #0072ff 100%);
    color: white;
    text-decoration: none;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 198, 255, 0.3);
}

.btn-1:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 198, 255, 0.4);
}

.body-content {
    background: rgba(15, 20, 25, 0.95);
    backdrop-filter: blur(10px);
    border-top: 2px solid rgba(0, 150, 255, 0.3);
}

.content-2 {
    padding: 40px 20px;
}

.content-2-cards {
    display: flex;
    justify-content: center;
    gap: 30px;
    max-width: 1200px;
    margin: 0 auto;
}

.card {
    flex: 1;
    max-width: 300px;
    background: linear-gradient(135deg, rgba(26, 35, 50, 0.8) 0%, rgba(37, 37, 53, 0.8) 100%);
    border-radius: 15px;
    border: 2px solid rgba(0, 150, 255, 0.3);
    box-shadow: 0 8px 25px rgba(0, 150, 255, 0.1);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 35px rgba(0, 150, 255, 0.2);
    border-color: rgba(0, 150, 255, 0.5);
}

.card-body {
    padding: 30px 20px;
    text-align: center;
    color: white;
}

.card-body img {
    filter: brightness(0) invert(1);
    margin-bottom: 20px;
}

.card-body h1 {
    font-size: 1.4em;
    margin-bottom: 15px;
    color: #00c6ff;
}

.card-body p {
    font-size: 0.9em;
    line-height: 1.5;
    margin-bottom: 20px;
    opacity: 0.8;
}

.read-link {
    color: #00ff88;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.read-link:hover {
    color: #00c6ff;
}

@media (max-height: 800px) {
    .first-look {
        height: 65vh;
    }
    
    .first-look-slogan {
        font-size: 42px;
    }
    
    .first-look-description {
        font-size: 14px;
        margin-top: 15px;
    }
    
    .first-look-buttons {
        margin-top: 20px;
    }
}

@media (max-height: 700px) {
    .first-look {
        height: 60vh;
    }
    
    .first-look-slogan {
        font-size: 36px;
    }
    
    .card-body {
        padding: 20px;
    }
}

@media (max-width: 1200px) {
    .content-2-cards {
        flex-direction: column;
        height: auto;
        max-height: 100%;
        overflow: hidden;
    }
    
    .card {
        width: 100%;
        max-width: none;
        height: 120px;
        margin-bottom: 10px;
    }
    
    .card-body {
        flex-direction: row;
        align-items: center;
        padding: 15px;
    }
    
    .card-body h1 {
        margin-right: 20px;
        min-width: 150px;
    }
}

.home-header {
    background: linear-gradient(135deg, #0f1b3c 0%, #1a2d5a 100%);
    padding: 20px 30px;
    text-align: center;
    border-bottom: 3px solid rgba(0, 150, 255, 0.4);
    box-shadow: 0 4px 25px rgba(0, 150, 255, 0.3);
    flex-shrink: 0;
    height: 120px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.home-title {
    font-size: 2.5em;
    font-weight: bold;
    background: linear-gradient(135deg, #00c6ff 0%, #0072ff 50%, #00d4ff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 30px rgba(0, 198, 255, 0.3);
    margin-bottom: 8px;
}

.home-subtitle {
    font-size: 1.1em;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 300;
    letter-spacing: 2px;
}

.home-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 20px 30px;
    overflow: hidden;
}

.intro-section {
    background: linear-gradient(135deg, rgba(15, 20, 25, 0.8) 0%, rgba(26, 35, 50, 0.8) 100%);
    border: 2px solid rgba(0, 150, 255, 0.3);
    border-radius: 15px;
    padding: 20px 30px;
    margin-bottom: 25px;
    backdrop-filter: blur(10px);
    box-shadow: 0 0 20px rgba(0, 150, 255, 0.1);
    flex-shrink: 0;
    height: 120px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.intro-text {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.1em;
    line-height: 1.6;
    text-align: center;
    margin: 0;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    flex: 1;
    overflow: hidden;
}

.feature-card {
    background: linear-gradient(135deg, rgba(15, 20, 25, 0.8) 0%, rgba(26, 35, 50, 0.8) 100%);
    border: 2px solid rgba(0, 150, 255, 0.3);
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    backdrop-filter: blur(10px);
    box-shadow: 0 0 20px rgba(0, 150, 255, 0.1);
    transition: all 0.3s ease;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    justify-content: center;
    min-height: 180px;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 150, 255, 0.3);
    border-color: rgba(0, 150, 255, 0.6);
}

.feature-icon {
    font-size: 2.5em;
    margin-bottom: 15px;
    display: block;
}

.feature-title {
    color: #00c6ff;
    font-size: 1.2em;
    font-weight: 600;
    margin-bottom: 10px;
}

.feature-desc {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9em;
    line-height: 1.4;
}
