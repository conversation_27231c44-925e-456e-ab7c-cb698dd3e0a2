.operation-management {
  height: 100vh;
  width: 100%;
  background: linear-gradient(135deg, #0a0e1a 0%, #1a2332 50%, #0a0e1a 100%);
  color: #fff;
  font-family: 'Microsoft YaHei', 'PingFang SC', sans-serif;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 头部样式 */
.om-header {
  background: linear-gradient(135deg, rgba(26, 35, 50, 0.9) 0%, rgba(37, 37, 53, 0.9) 100%);
  border-bottom: 2px solid rgba(0, 150, 255, 0.4);
  padding: 15px 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 4px 25px rgba(0, 150, 255, 0.3);
  backdrop-filter: blur(10px);
  flex-shrink: 0;
}

.om-header h1 {
  margin: 0;
  font-size: 1.8em;
  font-weight: 700;
  background: linear-gradient(135deg, #ffffff 0%, #00c6ff 30%, #0072ff 60%, #00d4ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 30px rgba(0, 200, 255, 0.5);
  letter-spacing: 2px;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.9em;
  color: rgba(255, 255, 255, 0.8);
}

.status-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #4ECDC4;
  box-shadow: 0 0 10px rgba(78, 205, 196, 0.6);
  animation: statusPulse 2s ease-in-out infinite;
}

@keyframes statusPulse {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.7; transform: scale(1.2); }
}

/* 主要内容区域 */
.om-content {
  flex: 1;
  padding: 15px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  gap: 12px;
  min-height: 0;
  height: calc(100vh - 70px);
}

/* 实时监控面板 */
.monitoring-panel {
  flex-shrink: 0;
  height: 100px;
}

.monitoring-cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 15px;
}

.monitor-card {
  background: linear-gradient(135deg, rgba(26, 35, 50, 0.9) 0%, rgba(37, 37, 53, 0.9) 100%);
  border: 2px solid rgba(0, 150, 255, 0.3);
  border-radius: 12px;
  padding: 15px;
  display: flex;
  align-items: center;
  gap: 12px;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 150, 255, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  height: 70px;
}

.monitor-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(0, 200, 255, 0.1), transparent);
  transition: left 0.8s ease;
}

.monitor-card:hover {
  border-color: rgba(0, 150, 255, 0.5);
  box-shadow: 0 6px 25px rgba(0, 150, 255, 0.15);
  transform: translateY(-2px);
}

.monitor-card:hover::before {
  left: 100%;
}

.card-icon {
  font-size: 1.5em;
  width: 45px;
  height: 45px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, rgba(0, 150, 255, 0.2), rgba(0, 200, 255, 0.1));
  border-radius: 50%;
  border: 2px solid rgba(0, 150, 255, 0.3);
}

.card-content {
  flex: 1;
}

.card-value {
  font-size: 1.4em;
  font-weight: 700;
  color: #00c6ff;
  margin-bottom: 3px;
  text-shadow: 0 0 10px rgba(0, 198, 255, 0.3);
}

.card-label {
  font-size: 0.9em;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
}

/* 主要功能区域 */
.main-functions {
  flex: 1;
  display: grid;
  grid-template-columns: 2fr 1fr;
  grid-template-rows: auto 2fr;
  gap: 12px;
  min-height: 0;
  overflow: hidden;
}

/* 调度方案区域 */
.schedule-section {
  background: linear-gradient(135deg, rgba(26, 35, 50, 0.9) 0%, rgba(37, 37, 53, 0.9) 100%);
  border: 2px solid rgba(0, 150, 255, 0.3);
  border-radius: 15px;
  padding: 18px;
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 30px rgba(0, 150, 255, 0.15);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  grid-row: span 2;
  min-height: 350px;
}

/* 收益预测区域 */
.revenue-section {
  background: linear-gradient(135deg, rgba(26, 35, 50, 0.9) 0%, rgba(37, 37, 53, 0.9) 100%);
  border: 2px solid rgba(0, 150, 255, 0.3);
  border-radius: 15px;
  padding: 10px;
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 30px rgba(0, 150, 255, 0.15);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  max-height: 160px; /* 减小高度 */
}

.revenue-content {
  flex: 1;
}

.revenue-cards {
  display: flex;
  flex-direction: row; /* 改为水平排列 */
  flex-wrap: wrap; /* 允许换行 */
  gap: 8px;
  justify-content: space-between;
}

.revenue-card {
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(0, 150, 255, 0.2);
  border-radius: 10px;
  padding: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  flex: 1;
  min-width: 120px;
  max-width: 180px;
}

.revenue-icon {
  font-size: 1.4em;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, rgba(255, 193, 7, 0.2), rgba(255, 152, 0, 0.1));
  border-radius: 50%;
  border: 2px solid rgba(255, 193, 7, 0.3);
}

.revenue-info {
  flex: 1;
}

.revenue-value {
  font-size: 1.2em;
  font-weight: 700;
  color: #FFD700;
  margin-bottom: 2px;
  text-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
}

.revenue-label {
  font-size: 0.8em;
  color: rgba(255, 255, 255, 0.7);
}

/* 对比分析区域 */
.comparison-section {
  background: linear-gradient(135deg, rgba(26, 35, 50, 0.9) 0%, rgba(37, 37, 53, 0.9) 100%);
  border: 2px solid rgba(0, 150, 255, 0.3);
  border-radius: 15px;
  padding: 16px;
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 30px rgba(0, 150, 255, 0.15);
  overflow: auto; /* 添加滚动条确保内容可见 */
  display: flex;
  flex-direction: column;
  min-height: 0; /* 允许收缩 */
  flex: 1; /* 占用剩余空间 */
}

.comparison-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 15px;
  overflow: auto; /* 添加滚动条 */
}

.comparison-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.comparison-item {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  padding: 12px;
  border: 1px solid rgba(0, 150, 255, 0.2);
}

.comparison-title {
  font-size: 1em;
  font-weight: 600;
  color: #00c6ff;
  margin-bottom: 15px;
  text-align: center;
}

.comparison-bars {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.comparison-bar {
  display: flex;
  align-items: center;
  gap: 10px;
}

.bar-label {
  min-width: 60px;
  font-size: 0.8em;
  color: rgba(255, 255, 255, 0.7);
}

.bar-fill {
  height: 8px;
  border-radius: 4px;
  transition: width 0.8s ease;
  position: relative;
  overflow: hidden;
}

.comparison-bar.before .bar-fill {
  background: linear-gradient(90deg, #FF6B6B, #FF8E53);
  box-shadow: 0 0 10px rgba(255, 107, 107, 0.3);
}

.comparison-bar.after .bar-fill {
  background: linear-gradient(90deg, #4ECDC4, #44A08D);
  box-shadow: 0 0 10px rgba(78, 205, 196, 0.3);
  animation: progressGlow 2s ease-in-out infinite;
}

.bar-value {
  min-width: 35px;
  font-size: 0.8em;
  font-weight: 600;
  color: #fff;
}

@keyframes progressGlow {
  0%, 100% { 
    box-shadow: 0 0 10px rgba(78, 205, 196, 0.3);
  }
  50% { 
    box-shadow: 0 0 15px rgba(78, 205, 196, 0.6);
  }
}

/* 对比卡片样式 */
.comparison-cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  margin-bottom: 15px; /* 添加底部间距 */
}

.comparison-card {
  background: linear-gradient(135deg, rgba(0, 30, 60, 0.6) 0%, rgba(0, 50, 100, 0.4) 100%);
  border: 2px solid rgba(0, 150, 255, 0.3);
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.comparison-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(0, 198, 255, 0.1), transparent);
  transition: left 0.6s ease;
}

.comparison-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 150, 255, 0.3);
  border-color: rgba(0, 200, 255, 0.6);
}

.comparison-card:hover::before {
  left: 100%;
}

.card-title {
  font-size: 1.1em;
  font-weight: 600;
  color: #00c6ff;
  margin-bottom: 15px;
}

.card-comparison {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.before-value, .after-value {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.before-value .label, .after-value .label {
  font-size: 0.8em;
  color: rgba(255, 255, 255, 0.7);
}

.before-value .value {
  font-size: 1.2em;
  font-weight: 600;
  color: #ff6b6b;
}

.after-value .value {
  font-size: 1.2em;
  font-weight: 600;
  color: #4ecdc4;
}

.improvement-arrow {
  font-size: 1.5em;
  color: #00c6ff;
  animation: arrowPulse 2s ease-in-out infinite;
}

@keyframes arrowPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.improvement-rate {
  font-size: 1.1em;
  font-weight: 700;
  color: #4ecdc4;
  background: rgba(78, 205, 196, 0.1);
  padding: 8px 12px;
  border-radius: 20px;
  border: 1px solid rgba(78, 205, 196, 0.3);
}

.improvement-rate.cost-reduction,
.improvement-rate.carbon-reduction {
  color: #66bb6a;
  background: rgba(102, 187, 106, 0.1);
  border-color: rgba(102, 187, 106, 0.3);
}

/* 24小时对比图表 */
.comparison-chart {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 12px;
  padding: 15px;
  border: 1px solid rgba(0, 150, 255, 0.2);
  min-height: 280px; /* 确保足够的高度 */
  display: flex;
  flex-direction: column;
  margin-bottom: 15px; /* 添加底部间距 */
}

.chart-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.chart-title h3 {
  margin: 0;
  color: #00c6ff;
  font-size: 1.2em;
}

.chart-legend {
  display: flex;
  gap: 20px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.9em;
  color: rgba(255, 255, 255, 0.8);
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.legend-item.before .legend-color {
  background: #ff6b6b;
}

.legend-item.after .legend-color {
  background: #4ecdc4;
}

.chart-container {
  position: relative;
  display: flex;
  height: 230px; /* 增加高度 */
  width: 100%;
  min-width: 0;
  overflow: hidden;
}

.chart-grid {
  flex: 1;
  display: flex;
  align-items: flex-end;
  gap: 3px;
  padding-right: 60px;
  min-width: 0;
  overflow: hidden;
}

.chart-column {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
  min-width: 0;
  max-width: none;
}

.chart-bars {
  display: flex;
  gap: 2px;
  align-items: flex-end;
  height: 200px; /* 增加高度 */
  width: 100%;
  justify-content: center;
}

.chart-bar {
  width: 6px;
  border-radius: 2px 2px 0 0;
  transition: all 0.3s ease;
  cursor: pointer;
}

.chart-bar.before {
  background: linear-gradient(to top, #ff6b6b, #ff8e53);
  box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
}

.chart-bar.after {
  background: linear-gradient(to top, #4ecdc4, #44a08d);
  box-shadow: 0 2px 8px rgba(78, 205, 196, 0.3);
}

.chart-bar:hover {
  transform: scaleY(1.05);
  filter: brightness(1.2);
}

.chart-label {
  font-size: 0.7em;
  color: rgba(255, 255, 255, 0.6);
  margin-top: 8px;
  transform: rotate(-45deg);
  white-space: nowrap;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 40px;
}

.chart-y-axis {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 200px; /* 匹配柱状图高度 */
  width: 60px;
  padding-left: 10px;
  flex-shrink: 0;
}

.y-label {
  font-size: 0.8em;
  color: rgba(255, 255, 255, 0.6);
  text-align: right;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 性能指标网格 */
.performance-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  margin-bottom: 10px; /* 添加底部间距 */
}

.performance-item {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  padding: 15px;
  border: 1px solid rgba(0, 150, 255, 0.2);
}

.performance-title {
  font-size: 1em;
  font-weight: 600;
  color: #00c6ff;
  margin-bottom: 15px;
  text-align: center;
}

.performance-bars {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.performance-bar {
  display: flex;
  align-items: center;
  gap: 10px;
}

.performance-bar .bar-label {
  min-width: 60px;
  font-size: 0.8em;
  color: rgba(255, 255, 255, 0.7);
}

.performance-bar .bar-fill {
  height: 8px;
  border-radius: 4px;
  transition: width 0.8s ease;
  position: relative;
  overflow: hidden;
}

.performance-bar.before .bar-fill {
  background: linear-gradient(90deg, #FF6B6B, #FF8E53);
  box-shadow: 0 0 10px rgba(255, 107, 107, 0.3);
}

.performance-bar.after .bar-fill {
  background: linear-gradient(90deg, #4ECDC4, #44A08D);
  box-shadow: 0 0 10px rgba(78, 205, 196, 0.3);
  animation: progressGlow 2s ease-in-out infinite;
}

.performance-bar .bar-value {
  min-width: 35px;
  font-size: 0.8em;
  font-weight: 600;
  color: #fff;
}

/* 执行计划样式 */
.execution-plan {
  margin-top: 20px;
  background: linear-gradient(135deg, rgba(0, 40, 80, 0.4) 0%, rgba(0, 60, 120, 0.3) 100%);
  border: 2px solid rgba(0, 150, 255, 0.3);
  border-radius: 12px;
  padding: 20px;
  animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.execution-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(0, 150, 255, 0.3);
}

.execution-header h3 {
  margin: 0;
  color: #00c6ff;
  font-size: 1.3em;
  font-weight: 600;
}

.plan-summary {
  display: flex;
  align-items: center;
  gap: 15px;
}

.total-steps {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9em;
  background: rgba(0, 150, 255, 0.2);
  padding: 5px 12px;
  border-radius: 15px;
}

.close-plan-btn {
  background: rgba(255, 107, 107, 0.2);
  border: 1px solid rgba(255, 107, 107, 0.5);
  color: #ff6b6b;
  padding: 5px 10px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1.1em;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-plan-btn:hover {
  background: rgba(255, 107, 107, 0.3);
  transform: scale(1.1);
}

.execution-steps {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.execution-step {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 10px;
  padding: 15px;
  border-left: 4px solid transparent;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.execution-step::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(0, 150, 255, 0.05), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.execution-step:hover::before {
  opacity: 1;
}

.execution-step.priority-高 {
  border-left-color: #ff6b6b;
  background: rgba(255, 107, 107, 0.05);
}

.execution-step.priority-中 {
  border-left-color: #ffd700;
  background: rgba(255, 215, 0, 0.05);
}

.execution-step.priority-低 {
  border-left-color: #4ecdc4;
  background: rgba(78, 205, 196, 0.05);
}

.step-number {
  flex-shrink: 0;
  width: 35px;
  height: 35px;
  background: linear-gradient(135deg, #00c6ff, #0072ff);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 0.9em;
  box-shadow: 0 3px 10px rgba(0, 198, 255, 0.3);
}

.step-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.step-main {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 20px;
}

.step-device {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.device-name {
  color: #ffffff;
  font-weight: 600;
  font-size: 1.1em;
}

.device-badge {
  padding: 3px 8px;
  border-radius: 12px;
  font-size: 0.75em;
  font-weight: 500;
  width: fit-content;
}

.device-badge.风电 {
  background: linear-gradient(135deg, #4ECDC4, #44A08D);
  color: white;
}

.device-badge.光伏 {
  background: linear-gradient(135deg, #FFE66D, #FFB347);
  color: #333;
}

.device-badge.储能 {
  background: linear-gradient(135deg, #FF6B6B, #FF8E53);
  color: white;
}

.device-badge.燃气 {
  background: linear-gradient(135deg, #95E1D3, #46C93A);
  color: #333;
}

.device-badge.水电 {
  background: linear-gradient(135deg, #A8E6CF, #7FCDCD);
  color: #333;
}

.step-action {
  display: flex;
  flex-direction: column;
  gap: 5px;
  text-align: right;
}

.action-text {
  color: #00c6ff;
  font-weight: 600;
  font-size: 1em;
}

.power-change {
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.9em;
}

.change-amount {
  color: #4ecdc4;
  font-weight: 600;
  margin-left: 5px;
}

.step-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 15px;
}

.step-priority, .step-time, .step-status {
  display: flex;
  align-items: center;
  gap: 5px;
}

.priority-badge {
  padding: 3px 8px;
  border-radius: 12px;
  font-size: 0.75em;
  font-weight: 600;
}

.priority-badge.priority-高 {
  background: rgba(255, 107, 107, 0.2);
  color: #ff6b6b;
  border: 1px solid rgba(255, 107, 107, 0.5);
}

.priority-badge.priority-中 {
  background: rgba(255, 215, 0, 0.2);
  color: #ffd700;
  border: 1px solid rgba(255, 215, 0, 0.5);
}

.priority-badge.priority-低 {
  background: rgba(78, 205, 196, 0.2);
  color: #4ecdc4;
  border: 1px solid rgba(78, 205, 196, 0.5);
}

.step-time {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.85em;
}

.time-icon {
  font-size: 0.9em;
}

.status-badge {
  padding: 3px 8px;
  border-radius: 12px;
  font-size: 0.75em;
  font-weight: 500;
  background: rgba(255, 193, 7, 0.2);
  color: #ffc107;
  border: 1px solid rgba(255, 193, 7, 0.5);
}

.execution-footer {
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid rgba(0, 150, 255, 0.3);
}

.execution-summary {
  display: flex;
  justify-content: space-around;
  align-items: center;
  gap: 20px;
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

.summary-label {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9em;
}

.summary-value {
  color: #00c6ff;
  font-weight: 600;
  font-size: 1.1em;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(0, 150, 255, 0.2);
}

.section-header h2 {
  margin: 0;
  font-size: 1.5em;
  color: #00c6ff;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 10px;
  text-align: left;
}



/* 生成按钮 */
.generate-btn {
  background: linear-gradient(135deg, #00c6ff 0%, #0072ff 100%);
  border: none;
  color: white;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 1em;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 198, 255, 0.3);
  position: relative;
  overflow: hidden;
}

.generate-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.generate-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 198, 255, 0.4);
}

.generate-btn:hover::before {
  left: 100%;
}

.generate-btn:active {
  transform: translateY(0);
}

/* 调度内容区域 */
.schedule-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.device-list {
  flex: 1;
  overflow: auto;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  padding: 15px;
  min-height: 250px;
  max-height: 350px;
  border: 1px solid rgba(0, 150, 255, 0.2);
  box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.3);
}

/* 调度表格样式 */
.device-list table {
  width: 100%;
  border-collapse: collapse;
  background: linear-gradient(135deg, rgba(0, 30, 60, 0.3) 0%, rgba(0, 50, 100, 0.2) 100%);
  border-radius: 8px;
  overflow: hidden;
  table-layout: fixed;
  box-shadow: 0 4px 15px rgba(0, 150, 255, 0.1);
}

.device-list table thead {
  background: linear-gradient(135deg, rgba(0, 150, 255, 0.4) 0%, rgba(0, 200, 255, 0.3) 100%);
  position: sticky;
  top: 0;
  z-index: 10;
}

.device-list table thead th {
  position: relative;
  background: rgba(0, 100, 200, 0.2);
  backdrop-filter: blur(10px);
}

.device-list table thead th::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, 
    transparent 0%, 
    #00c6ff 20%, 
    #0096ff 50%, 
    #00c6ff 80%, 
    transparent 100%);
  box-shadow: 0 0 10px rgba(0, 198, 255, 0.5);
}



.device-list table th {
  padding: 18px 12px;
  text-align: left;
  border-bottom: 2px solid rgba(0, 150, 255, 0.3);
  color: #ffffff;
  font-weight: 700;
  font-size: 0.95em;
  letter-spacing: 1.2px;
  vertical-align: middle;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
}

.device-list table th:hover {
  color: #00ff88;
  text-shadow: 0 0 15px rgba(0, 255, 136, 0.6);
}

.device-list table th:nth-child(1) {
  text-align: left;
  width: 18%;
  padding-left: 20px;
}

.device-list table th:nth-child(2) {
  text-align: center;
  width: 12%;
}

.device-list table th:nth-child(3) {
  text-align: center;
  width: 14%;
}

.device-list table th:nth-child(4) {
  text-align: center;
  width: 14%;
}

.device-list table th:nth-child(5) {
  text-align: center;
  width: 14%;
}

.device-list table th:nth-child(6) {
  text-align: center;
  width: 14%;
}

.device-list table th:nth-child(7) {
  text-align: center;
  width: 14%;
}

.device-list table td {
  padding: 16px 12px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.15);
  transition: all 0.3s ease;
  vertical-align: middle;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: center;
  font-size: 0.92em;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.95);
}

.device-list table td:nth-child(1) {
  text-align: left;
  font-weight: 600;
  width: 18%;
  padding-left: 20px;
  color: #ffffff;
}

.device-list table td:nth-child(2) {
  text-align: center;
  width: 12%;
}

.device-list table td:nth-child(3) {
  text-align: center;
  width: 14%;
  font-weight: 700;
  color: #4ECDC4;
  text-shadow: 0 0 8px rgba(78, 205, 196, 0.5);
}

.device-list table td:nth-child(4) {
  text-align: center;
  width: 14%;
  font-weight: 700;
  color: #66BB6A;
  text-shadow: 0 0 8px rgba(102, 187, 106, 0.5);
}

.device-list table td:nth-child(5) {
  text-align: center;
  width: 14%;
  font-weight: 700;
  color: #FFD700;
  text-shadow: 0 0 8px rgba(255, 215, 0, 0.5);
}

.device-list table td:nth-child(6) {
  text-align: center;
  width: 14%;
}

.device-list table td:nth-child(7) {
  text-align: center;
  width: 14%;
}

.schedule-table tbody tr {
  transition: all 0.3s ease;
}
.device-list table tbody tr:hover {
  background: linear-gradient(135deg, rgba(0, 150, 255, 0.2) 0%, rgba(0, 200, 255, 0.1) 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 150, 255, 0.3);
  cursor: pointer;
}

.device-list table tbody tr:nth-child(even) {
  background: rgba(0, 30, 60, 0.2);
}

.device-list table tbody tr:nth-child(odd) {
  background: rgba(0, 20, 40, 0.15);
}

.device-list table tbody tr {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-left: 3px solid transparent;
}

.device-list table tbody tr:hover {
  border-left: 3px solid #00c6ff;
}

/* 设备类型标签 */
.device-type {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.82em;
  font-weight: 600;
  text-align: center;
  min-width: 60px;
  display: inline-block;
  position: relative;
  overflow: hidden;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.device-type.风电 {
  background: linear-gradient(135deg, #4ECDC4, #44A08D);
  color: white;
}

.device-type.光伏 {
  background: linear-gradient(135deg, #FFE66D, #FFB347);
  color: #333;
}

.device-type.储能 {
  background: linear-gradient(135deg, #FF6B6B, #FF8E53);
  color: white;
}

.device-type.燃气 {
  background: linear-gradient(135deg, #95E1D3, #46C93A);
  color: #333;
}

.device-type.水电 {
  background: linear-gradient(135deg, #A8E6CF, #7FCDCD);
  color: #333;
}

/* 状态标签 */
.status {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.82em;
  font-weight: 600;
  text-align: center;
  min-width: 60px;
  display: inline-block;
  position: relative;
  overflow: hidden;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
}

.status.运行 {
  background: linear-gradient(135deg, #4ECDC4, #44A08D);
  color: white;
  box-shadow: 0 0 10px rgba(78, 205, 196, 0.3);
}

.status.停机 {
  background: linear-gradient(135deg, #FF6B6B, #FF5722);
  color: white;
}

.status.维护 {
  background: linear-gradient(135deg, #FFE66D, #FFC107);
  color: #333;
}

.status.待机 {
  background: linear-gradient(135deg, #B0BEC5, #78909C);
  color: white;
}

.status.充电 {
  background: linear-gradient(135deg, #81C784, #4CAF50);
  color: white;
  animation: chargePulse 2s ease-in-out infinite;
}

.status.放电 {
  background: linear-gradient(135deg, #FFB74D, #FF9800);
  color: white;
  animation: dischargePulse 2s ease-in-out infinite;
}

@keyframes chargePulse {
  0%, 100% { box-shadow: 0 0 5px rgba(129, 199, 132, 0.5); }
  50% { box-shadow: 0 0 15px rgba(129, 199, 132, 0.8); }
}

@keyframes dischargePulse {
  0%, 100% { box-shadow: 0 0 5px rgba(255, 183, 77, 0.5); }
  50% { box-shadow: 0 0 15px rgba(255, 183, 77, 0.8); }
}

/* 编辑按钮 */
.edit-btn {
  background: linear-gradient(135deg, #00c6ff, #0072ff);
  border: 2px solid rgba(0, 198, 255, 0.6);
  color: white;
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 0.9em;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(0, 198, 255, 0.3);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  min-width: 60px;
  white-space: nowrap;
}

.edit-btn:hover {
  background: linear-gradient(135deg, #00d4ff, #0088ff);
  border-color: rgba(0, 212, 255, 0.8);
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(0, 198, 255, 0.5);
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.7);
}

.edit-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(0, 198, 255, 0.3);
}

/* 设备编辑模态框 */
.device-edit-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
  animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    backdrop-filter: blur(0px);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(5px);
  }
}

.modal-content {
  background: linear-gradient(135deg, rgba(8, 18, 35, 0.98) 0%, rgba(15, 25, 45, 0.98) 100%);
  border: 2px solid rgba(0, 150, 255, 0.5);
  border-radius: 15px;
  padding: 30px;
  min-width: 400px;
  max-width: 500px;
  box-shadow: 0 20px 60px rgba(0, 150, 255, 0.4), 
              0 0 30px rgba(0, 150, 255, 0.2);
  backdrop-filter: blur(15px);
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    transform: translateY(-50px) scale(0.9);
    opacity: 0;
  }
  to {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

.modal-content h3 {
  margin: 0 0 20px 0;
  color: #00c6ff;
  font-size: 1.4em;
  text-align: center;
  font-weight: 600;
}

.modal-content p {
  margin: 0 0 20px 0;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
  font-size: 1.1em;
}

/* 输入组样式 */
.input-group {
  margin-bottom: 20px;
}

.input-group label {
  display: block;
  margin-bottom: 8px;
  color: #00c6ff;
  font-weight: 500;
  font-size: 0.95em;
}

.input-group input,
.input-group select {
  width: 100%;
  padding: 12px 15px;
  background: rgba(15, 25, 40, 0.8);
  border: 1px solid rgba(0, 150, 255, 0.4);
  border-radius: 8px;
  color: #fff;
  font-size: 1em;
  transition: all 0.3s ease;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.3);
}

.input-group input:focus,
.input-group select:focus {
  outline: none;
  border-color: #00c6ff;
  box-shadow: 0 0 0 3px rgba(0, 198, 255, 0.3), inset 0 2px 4px rgba(0, 0, 0, 0.3);
  background: rgba(0, 150, 255, 0.15);
}

.input-group select option {
  background: rgba(15, 25, 40, 0.95);
  color: #fff;
  padding: 10px;
  border: none;
}

.input-group select option:hover {
  background: rgba(0, 150, 255, 0.2);
}

.input-group select option:checked {
  background: rgba(0, 150, 255, 0.3);
  color: #00c6ff;
}

.input-group input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

/* 模态框操作按钮 */
.modal-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  margin-top: 30px;
}

.confirm-btn {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  border: none;
  color: white;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 1em;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

.confirm-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
}

.cancel-btn {
  background: linear-gradient(135deg, #f44336, #d32f2f);
  border: none;
  color: white;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 1em;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(244, 67, 54, 0.3);
}

.cancel-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(244, 67, 54, 0.4);
}

/* 滚动条样式 */
.device-list::-webkit-scrollbar {
  width: 8px;
}

.device-list::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.device-list::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, rgba(0, 150, 255, 0.6), rgba(0, 100, 200, 0.6));
  border-radius: 4px;
}

.device-list::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, rgba(0, 150, 255, 0.8), rgba(0, 100, 200, 0.8));
}

/* 响应式设计 */
@media (max-height: 800px) {
  .om-header {
    padding: 8px 20px;
  }
  
  .om-header h1 {
    font-size: 1.5em;
  }
  
  .om-content {
    padding: 10px;
    gap: 8px;
    height: calc(100vh - 60px);
  }
  
  .monitoring-panel {
    height: 100px;
  }
  
  .monitoring-cards {
    gap: 8px;
  }
  
  .monitor-card {
    padding: 12px;
  }
  
  .card-value {
    font-size: 1.3em;
  }
  
  .schedule-section,
  .revenue-section,
  .comparison-section {
    padding: 12px;
  }
  
  .schedule-section {
    min-height: 280px;
  }
  
  .device-list {
    min-height: 180px;
    max-height: 200px;
  }
  
  .schedule-table th,
  .schedule-table td {
    padding: 6px;
    font-size: 0.85em;
  }
}

@media (max-width: 1400px) {
  .monitoring-cards {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .main-functions {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto auto;
  }
  
  .schedule-section {
    grid-row: span 1;
    min-height: 300px;
  }
  
  .device-list {
    max-height: 250px;
  }
}

@media (max-width: 1200px) {
  .modal-content {
    min-width: 350px;
    padding: 25px;
  }
  
  .schedule-table {
    font-size: 0.9em;
  }
  
  .device-type,
  .status {
    font-size: 0.75em;
    padding: 3px 6px;
  }
  
  .monitor-card {
    padding: 15px;
  }
  
  .card-icon {
    width: 50px;
    height: 50px;
    font-size: 1.5em;
  }
  
  .card-value {
    font-size: 1.4em;
  }
}

@media (max-width: 768px) {
  .om-header {
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }
  
  .monitoring-cards {
    grid-template-columns: 1fr;
  }
  
  .section-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .modal-content {
    margin: 20px;
    min-width: auto;
    width: calc(100% - 40px);
  }
  
  .modal-actions {
    flex-direction: column;
  }
  
  .schedule-table {
    font-size: 0.8em;
  }
  
  .schedule-table th,
  .schedule-table td {
    padding: 6px 4px;
  }
  
  .monitor-card {
    flex-direction: column;
    text-align: center;
    padding: 15px;
  }
  
  .card-icon {
    margin-bottom: 10px;
  }
  
  .revenue-cards {
    gap: 10px;
  }
  
  .revenue-card {
    padding: 12px;
  }
  
  .comparison-cards {
    grid-template-columns: 1fr;
    gap: 10px;
  }
  
  .comparison-chart {
    padding: 15px;
  }
  
  .chart-grid {
    gap: 2px;
  }
  
  .chart-bar {
    width: 4px;
  }
  
  .chart-label {
    font-size: 0.6em;
  }
  
  .performance-grid {
    grid-template-columns: 1fr;
    gap: 10px;
  }
  
  .execution-plan {
    padding: 15px;
  }
  
  .execution-header {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }
  
  .step-main {
    flex-direction: column;
    gap: 10px;
  }
  
  .step-action {
    text-align: left;
  }
  
  .step-meta {
    flex-wrap: wrap;
    gap: 8px;
  }
  
  .execution-summary {
    flex-direction: column;
    gap: 15px;
  }
}

@media (max-width: 480px) {
  .om-content {
    padding: 10px;
  }
  
  .monitor-card,
  .schedule-section,
  .revenue-section,
  .comparison-section {
    padding: 12px;
  }
  
  .schedule-table {
    font-size: 0.75em;
  }
  
  .schedule-table th,
  .schedule-table td {
    padding: 4px 2px;
  }
  
  .comparison-cards {
    grid-template-columns: 1fr;
    gap: 8px;
  }
  
  .comparison-card {
    padding: 15px;
  }
  
  .card-comparison {
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }
  
  .improvement-arrow {
    transform: rotate(90deg);
  }
  
  .comparison-chart {
    padding: 12px;
  }
  
  .chart-grid {
    gap: 1px;
  }
  
  .chart-bar {
    width: 3px;
  }
  
  .chart-label {
    font-size: 0.5em;
  }
  
  .chart-y-axis {
    width: 50px;
  }
  
  .y-label {
    font-size: 0.7em;
  }
  
  .execution-plan {
    padding: 12px;
  }
  
  .execution-step {
    padding: 12px;
    gap: 10px;
  }
  
  .step-number {
    width: 30px;
    height: 30px;
    font-size: 0.8em;
  }
  
  .device-name {
    font-size: 1em;
  }
  
  .step-meta {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
  
  .step-meta > div {
    justify-content: center;
  }
}

/* 科技感效果 */
.schedule-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    45deg,
    transparent 0%,
    rgba(0, 200, 255, 0.03) 25%,
    rgba(0, 200, 255, 0.05) 50%,
    rgba(0, 200, 255, 0.03) 75%,
    transparent 100%
  );
  pointer-events: none;
  animation: techGlow 4s ease-in-out infinite;
}

@keyframes techGlow {
  0%, 100% { opacity: 0.5; }
  50% { opacity: 1; }
}

/* 表格行动画 */
.schedule-table tbody tr {
  position: relative;
}

.schedule-table tbody tr::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 3px;
  background: linear-gradient(180deg, transparent, #00c6ff, transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.schedule-table tbody tr:hover::before {
  opacity: 1;
}

/* 优化方案样式 */
.optimization-plan {
  background: linear-gradient(135deg, rgba(26, 35, 50, 0.95) 0%, rgba(37, 37, 53, 0.95) 100%);
  border: 1px solid rgba(0, 150, 255, 0.3);
  border-radius: 15px;
  padding: 25px;
  margin-top: 20px;
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 150, 255, 0.2);
  max-height: 80vh;
  overflow-y: auto;
}

.plan-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(0, 150, 255, 0.2);
}

.plan-title h3 {
  margin: 0 0 8px 0;
  font-size: 1.4em;
  color: #00c6ff;
  font-weight: 600;
}

.plan-meta {
  display: flex;
  gap: 20px;
  font-size: 0.85em;
  color: rgba(255, 255, 255, 0.7);
}

.plan-id, .plan-time {
  background: rgba(0, 150, 255, 0.1);
  padding: 4px 8px;
  border-radius: 6px;
  border: 1px solid rgba(0, 150, 255, 0.2);
}

.close-plan-btn {
  background: rgba(255, 69, 58, 0.2);
  border: 1px solid rgba(255, 69, 58, 0.4);
  color: #ff453a;
  padding: 8px 12px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1.1em;
  transition: all 0.3s ease;
}

.close-plan-btn:hover {
  background: rgba(255, 69, 58, 0.3);
  transform: scale(1.05);
}

/* 优化目标概览 */
.optimization-overview {
  margin-bottom: 25px;
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.overview-header h4 {
  margin: 0;
  color: #00c6ff;
  font-size: 1.1em;
}

.optimization-type {
  display: flex;
  gap: 10px;
  align-items: center;
}

.type-badge {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8em;
  font-weight: 500;
}

.target-function {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.85em;
}

.objectives-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.objective-card {
  background: linear-gradient(135deg, rgba(0, 150, 255, 0.1) 0%, rgba(0, 200, 255, 0.05) 100%);
  border: 1px solid rgba(0, 150, 255, 0.2);
  border-radius: 12px;
  padding: 15px;
  display: flex;
  align-items: center;
  gap: 12px;
  transition: all 0.3s ease;
}

.objective-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 150, 255, 0.3);
}

.objective-icon {
  font-size: 1.8em;
}

.objective-value {
  font-size: 1.3em;
  font-weight: 600;
  color: #00c6ff;
}

.objective-label {
  font-size: 0.85em;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 2px;
}

/* 对比表格 */
.comparison-section {
  margin-bottom: 25px;
}

.comparison-section h4 {
  margin: 0 0 15px 0;
  color: #00c6ff;
  font-size: 1.1em;
}

.comparison-table {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 10px;
  overflow: hidden;
  border: 1px solid rgba(0, 150, 255, 0.2);
}

.comparison-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: 1px;
  background: rgba(0, 150, 255, 0.1);
}

.comparison-row.header {
  background: rgba(0, 150, 255, 0.2);
}

.comparison-item {
  background: rgba(26, 35, 50, 0.8);
  padding: 12px 15px;
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.9em;
}

.comparison-row.header .comparison-item {
  background: rgba(0, 150, 255, 0.3);
  font-weight: 600;
  color: white;
}

.comparison-item.improvement {
  color: #4ECDC4;
  font-weight: 600;
}

/* 设备调整方案 */
.device-adjustments {
  margin-bottom: 25px;
}

.device-adjustments h4 {
  margin: 0 0 15px 0;
  color: #00c6ff;
  font-size: 1.1em;
}

.adjustments-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.adjustment-card {
  background: linear-gradient(135deg, rgba(0, 150, 255, 0.08) 0%, rgba(0, 200, 255, 0.04) 100%);
  border: 1px solid rgba(0, 150, 255, 0.2);
  border-radius: 12px;
  padding: 15px;
  transition: all 0.3s ease;
}

.adjustment-card:hover {
  transform: translateX(5px);
  box-shadow: 0 5px 20px rgba(0, 150, 255, 0.2);
}

.adjustment-card.priority-高 {
  border-left: 4px solid #ff453a;
}

.adjustment-card.priority-中 {
  border-left: 4px solid #ff9500;
}

.adjustment-card.priority-低 {
  border-left: 4px solid #4ECDC4;
}

.adjustment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.device-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.device-name {
  font-weight: 600;
  color: white;
  font-size: 1em;
}

.device-type-badge {
  background: rgba(0, 150, 255, 0.2);
  color: #00c6ff;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.75em;
  border: 1px solid rgba(0, 150, 255, 0.3);
}

.priority-badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.75em;
  font-weight: 500;
}

.priority-badge.priority-高 {
  background: rgba(255, 69, 58, 0.2);
  color: #ff453a;
  border: 1px solid rgba(255, 69, 58, 0.3);
}

.priority-badge.priority-中 {
  background: rgba(255, 149, 0, 0.2);
  color: #ff9500;
  border: 1px solid rgba(255, 149, 0, 0.3);
}

.priority-badge.priority-低 {
  background: rgba(78, 205, 196, 0.2);
  color: #4ECDC4;
  border: 1px solid rgba(78, 205, 196, 0.3);
}

.power-adjustment {
  display: flex;
  align-items: center;
  gap: 8px;
}

.power-change {
  font-weight: 600;
  color: white;
  font-size: 0.95em;
}

.change-indicator {
  font-size: 0.85em;
  font-weight: 500;
}

.change-indicator.increase {
  color: #4ECDC4;
}

.change-indicator.decrease {
  color: #ff9500;
}

.adjustment-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.adjustment-reason, .expected-benefit {
  display: flex;
  gap: 8px;
  font-size: 0.85em;
}

.reason-label, .benefit-label {
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
  min-width: 70px;
}

.reason-text, .benefit-text {
  color: rgba(255, 255, 255, 0.9);
  flex: 1;
}

.adjustment-meta {
  display: flex;
  gap: 15px;
  margin-top: 5px;
  font-size: 0.8em;
  color: rgba(255, 255, 255, 0.7);
}

.implementation-time, .cost-impact {
  display: flex;
  align-items: center;
  gap: 4px;
}

.meta-icon {
  font-size: 0.9em;
}

/* 经济效益分析 */
.economic-benefits {
  margin-bottom: 25px;
}

.economic-benefits h4 {
  margin: 0 0 15px 0;
  color: #00c6ff;
  font-size: 1.1em;
}

.benefits-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 12px;
}

.benefit-card {
  background: linear-gradient(135deg, rgba(78, 205, 196, 0.1) 0%, rgba(78, 205, 196, 0.05) 100%);
  border: 1px solid rgba(78, 205, 196, 0.2);
  border-radius: 12px;
  padding: 15px;
  display: flex;
  align-items: center;
  gap: 10px;
  transition: all 0.3s ease;
}

.benefit-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(78, 205, 196, 0.3);
}

.benefit-icon {
  font-size: 1.5em;
}

.benefit-value {
  font-size: 1.2em;
  font-weight: 600;
  color: #4ECDC4;
}

.benefit-label {
  font-size: 0.8em;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 2px;
}

/* 实施建议 */
.recommendations {
  margin-bottom: 25px;
}

.recommendations h4 {
  margin: 0 0 15px 0;
  color: #00c6ff;
  font-size: 1.1em;
}

.recommendations-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.recommendation-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  background: rgba(0, 150, 255, 0.05);
  border: 1px solid rgba(0, 150, 255, 0.1);
  border-radius: 10px;
  padding: 12px 15px;
  transition: all 0.3s ease;
}

.recommendation-item:hover {
  background: rgba(0, 150, 255, 0.08);
  border-color: rgba(0, 150, 255, 0.2);
}

.recommendation-number {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8em;
  font-weight: 600;
  flex-shrink: 0;
  margin-top: 2px;
}

.recommendation-text {
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.9em;
  line-height: 1.4;
}

/* 风险评估 */
.risk-assessment h4 {
  margin: 0 0 15px 0;
  color: #00c6ff;
  font-size: 1.1em;
}

.risk-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.risk-item {
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  padding: 12px 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.risk-item.overall {
  grid-column: 1 / -1;
  background: rgba(0, 150, 255, 0.1);
  border-color: rgba(0, 150, 255, 0.2);
}

.risk-label {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9em;
}

.risk-level {
  padding: 4px 10px;
  border-radius: 15px;
  font-size: 0.8em;
  font-weight: 500;
}

.risk-level.低, .risk-level.极低 {
  background: rgba(78, 205, 196, 0.2);
  color: #4ECDC4;
  border: 1px solid rgba(78, 205, 196, 0.3);
}

.risk-level.中 {
  background: rgba(255, 149, 0, 0.2);
  color: #ff9500;
  border: 1px solid rgba(255, 149, 0, 0.3);
}

.risk-level.高 {
  background: rgba(255, 69, 58, 0.2);
  color: #ff453a;
  border: 1px solid rgba(255, 69, 58, 0.3);
}

/* 滚动条样式 */
.optimization-plan::-webkit-scrollbar {
  width: 8px;
}

.optimization-plan::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

.optimization-plan::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #00c6ff, #0072ff);
  border-radius: 4px;
}

.optimization-plan::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #00d4ff, #0080ff);
}

/* 针对100%缩放和标准屏幕的优化 */
@media screen and (min-width: 1200px) and (max-width: 1600px) {
  .comparison-chart {
    padding: 18px;
    min-height: 280px;
  }

  .chart-container {
    height: 220px;
  }

  .chart-grid {
    gap: 2px;
    padding-right: 50px;
  }

  .chart-bar {
    width: 5px;
  }

  .chart-label {
    font-size: 0.65em;
    max-width: 35px;
  }

  .chart-bars {
    height: 180px;
  }

  .chart-y-axis {
    height: 180px;
    width: 50px;
  }

  .y-label {
    font-size: 0.75em;
  }

  .comparison-section {
    padding: 18px;
  }

  .comparison-content {
    gap: 18px;
  }
}

/* 针对大屏幕的优化 */
@media screen and (min-width: 1600px) {
  .comparison-chart {
    padding: 25px;
    min-height: 320px;
  }

  .chart-container {
    height: 250px;
  }

  .chart-grid {
    gap: 4px;
    padding-right: 70px;
  }

  .chart-bar {
    width: 8px;
  }

  .chart-label {
    font-size: 0.75em;
    max-width: 50px;
  }

  .chart-bars {
    height: 200px;
  }

  .chart-y-axis {
    height: 200px;
    width: 70px;
  }

  .y-label {
    font-size: 0.85em;
  }

  .comparison-section {
    padding: 25px;
  }

  .comparison-content {
    gap: 25px;
  }
}

/* 针对超宽屏的优化 */
@media screen and (min-width: 1920px) {
  .comparison-chart {
    padding: 30px;
    min-height: 350px;
  }

  .chart-container {
    height: 280px;
  }

  .chart-grid {
    gap: 5px;
    padding-right: 80px;
  }

  .chart-bar {
    width: 10px;
  }

  .chart-label {
    font-size: 0.8em;
    max-width: 60px;
  }

  .chart-bars {
    height: 230px;
  }

  .chart-y-axis {
    height: 230px;
    width: 80px;
  }

  .y-label {
    font-size: 0.9em;
  }

  .comparison-section {
    padding: 30px;
  }

  .comparison-content {
    gap: 30px;
  }
}
