.resource-access-container {
    width: 100%;
    height: 100%;
    padding: 15px;
    background: linear-gradient(135deg, #0f1419 0%, #1a2332 100%);
    color: #fff;
    overflow: hidden;
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    z-index: 100;
    display: flex;
    flex-direction: column;
}

/* 页面标题区域 */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 20px;
    background: linear-gradient(135deg, rgba(0, 212, 255, 0.15) 0%, rgba(0, 150, 255, 0.1) 100%);
    border: 1px solid rgba(0, 212, 255, 0.4);
    border-radius: 12px;
    margin-bottom: 15px;
    backdrop-filter: blur(15px);
    flex-shrink: 0;
    height: 70px;
    box-shadow: 0 8px 32px rgba(0, 212, 255, 0.1);
}

.title-section {
    display: flex;
    align-items: center;
    gap: 15px;
}

.title-icon-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, rgba(0, 212, 255, 0.2) 0%, rgba(0, 150, 255, 0.3) 100%);
    border-radius: 12px;
    border: 1px solid rgba(0, 212, 255, 0.3);
    backdrop-filter: blur(10px);
}

.title-icon-wrapper .title-icon {
    font-size: 28px;
    filter: drop-shadow(0 0 10px rgba(0, 212, 255, 0.8));
}

.title-content {
    display: flex;
    flex-direction: column;
    gap: 5px;

}

.page-title {
    font-size: 24px;
    font-weight: 700;
    color: #ffffff;
    text-shadow: 0 0 15px rgba(0, 212, 255, 0.8);
    margin: 0;
    letter-spacing: 1px;
}

.page-subtitle {
    font-size: 12px;
    color: rgba(0, 212, 255, 0.8);
    margin: 0;
    font-weight: 400;
    letter-spacing: 0.5px;
    text-transform: uppercase;
}

.header-stats {
    display: flex;
    gap: 20px;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px 15px;
    background: rgba(0, 212, 255, 0.08);
    border: 1px solid rgba(0, 212, 255, 0.2);
    border-radius: 8px;
    backdrop-filter: blur(10px);
    min-width: 120px;
    transition: all 0.3s ease;
}

.stat-item:hover {
    background: rgba(0, 212, 255, 0.15);
    border-color: rgba(0, 212, 255, 0.4);
}

.stat-icon {
    font-size: 20px;
    filter: drop-shadow(0 0 8px rgba(0, 212, 255, 0.6));
}

.stat-content {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.stat-value {
    font-size: 18px;
    font-weight: bold;
    color: #ffffff;
    text-shadow: 0 0 10px rgba(0, 212, 255, 0.6);
    line-height: 1;
    margin-bottom: 2px;
}

.stat-label {
    font-size: 11px;
    color: rgba(255, 255, 255, 0.7);
    font-weight: 400;
}

/* 图表网格布局 */
.charts-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
    flex: 1;
    height: calc(100% - 100px);
    overflow: hidden;
}

/* 图表卡片样式 */
.chart-card {
    background: linear-gradient(135deg, rgba(26, 35, 50, 0.9) 0%, rgba(15, 20, 25, 0.9) 100%);
    border-radius: 15px;
    border: 1px solid rgba(0, 212, 255, 0.3);
    box-shadow: 0 8px 32px rgba(0, 212, 255, 0.1);
    padding: 15px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.chart-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, #00d4ff, transparent);
    animation: scan 2s linear infinite;
}

@keyframes scan {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.chart-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 212, 255, 0.2);
    border-color: rgba(0, 212, 255, 0.5);
}

.card-title {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
    font-size: 14px;
    font-weight: bold;
    color: #00d4ff;
    text-shadow: 0 0 8px rgba(0, 212, 255, 0.5);
    flex-shrink: 0;
}

.title-icon {
    font-size: 18px;
    filter: drop-shadow(0 0 5px rgba(0, 212, 255, 0.8));
}

/* 响应式设计 */
@media (max-width: 1600px) {
    .charts-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 1200px) {
    .charts-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .chart-card {
        padding: 15px;
    }
    
    .header-stats {
        gap: 20px;
    }
    
    .stat-item {
        padding: 8px 15px;
    }
}

@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        gap: 20px;
    }
    
    .header-stats {
        flex-wrap: wrap;
        justify-content: center;
        gap: 15px;
    }
    
    .page-title {
        font-size: 24px;
    }
}

/* 加载动画 */
.chart-card .echarts-loading {
    background: rgba(15, 20, 25, 0.9);
    color: #00d4ff;
}

/* 工具提示样式优化 */
.chart-card .echarts-tooltip {
    background: rgba(26, 35, 50, 0.95) !important;
    border: 1px solid rgba(0, 212, 255, 0.5) !important;
    border-radius: 8px !important;
    color: #fff !important;
    box-shadow: 0 8px 32px rgba(0, 212, 255, 0.2) !important;
}

.resource-access {
    padding: 20px;
    height: 100%;
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.module-header {
    margin-bottom: 30px;
    text-align: center;
}

.module-header h2 {
    color: white;
    font-size: 2.5em;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.module-description {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.1em;
}

.access-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 25px;
    margin-top: 30px;
}

.access-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    overflow: hidden;
    transition: transform 0.3s ease;
}

.access-card:hover {
    transform: translateY(-5px);
}

.access-card.primary .card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.access-card.secondary .card-header {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.access-card.accent .card-header {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.access-card.info .card-header {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.card-header {
    color: white;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h3 {
    margin: 0;
    font-size: 1.4em;
    font-weight: 600;
}

.resource-count {
    background: rgba(255, 255, 255, 0.2);
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.9em;
    font-weight: 500;
}

.card-content {
    padding: 25px;
}

.resource-types {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.type-item {
    display: flex;
    align-items: center;
    padding: 15px;
    background: rgba(102, 126, 234, 0.05);
    border-radius: 10px;
    border-left: 4px solid #667eea;
}

.type-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
}

.type-icon.solar {
    background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
}

.type-icon.wind {
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
}

.type-icon.hydro {
    background: linear-gradient(135deg, #81ecec 0%, #00b894 100%);
}

.type-icon::before {
    content: "⚡";
}

.type-info {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.type-name {
    font-weight: 600;
    color: #333;
    margin-bottom: 3px;
}

.type-capacity {
    color: #666;
    font-size: 0.9em;
}

.type-status {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8em;
    font-weight: 600;
}

.type-status.connected {
    background: rgba(40, 167, 69, 0.1);
    color: #28a745;
}

.type-status.pending {
    background: rgba(255, 193, 7, 0.1);
    color: #ffc107;
}

.storage-overview {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-bottom: 20px;
}

.storage-item {
    display: flex;
    align-items: center;
    padding: 15px;
    background: rgba(240, 147, 251, 0.05);
    border-radius: 10px;
    border-left: 4px solid #f093fb;
}

.storage-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
}

.storage-icon.battery {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.storage-icon.compressed {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.storage-icon::before {
    content: "🔋";
}

.storage-details {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.storage-name {
    font-weight: 600;
    color: #333;
    margin-bottom: 3px;
}

.storage-specs {
    color: #666;
    font-size: 0.9em;
}

.connection-status {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8em;
    font-weight: 600;
}

.connection-status.online {
    background: rgba(40, 167, 69, 0.1);
    color: #28a745;
}

.connection-status.maintenance {
    background: rgba(255, 193, 7, 0.1);
    color: #ffc107;
}

.add-resource-btn {
    width: 100%;
    padding: 12px;
    border: 2px dashed #f093fb;
    border-radius: 10px;
    background: transparent;
    color: #f093fb;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.add-resource-btn:hover {
    background: rgba(240, 147, 251, 0.1);
    transform: translateY(-2px);
}

.load-categories {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.category-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: rgba(79, 172, 254, 0.05);
    border-radius: 10px;
    border-left: 4px solid #4facfe;
}

.category-name {
    font-weight: 600;
    color: #333;
}

.category-count {
    color: #666;
    font-size: 0.9em;
}

.category-bar {
    width: 100px;
    height: 6px;
    background: #e9ecef;
    border-radius: 3px;
    overflow: hidden;
    margin-left: 15px;
}

.bar-fill {
    height: 100%;
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    transition: width 0.3s ease;
}

.access-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    margin-bottom: 25px;
}

.stat-block {
    text-align: center;
    padding: 20px;
    background: rgba(67, 233, 123, 0.05);
    border-radius: 10px;
    border-left: 4px solid #43e97b;
}

.stat-number {
    font-size: 2.5em;
    font-weight: bold;
    color: #43e97b;
    margin-bottom: 5px;
}

.stat-label {
    color: #666;
    font-size: 0.9em;
    font-weight: 500;
}

.recent-access h4 {
    margin-bottom: 15px;
    color: #333;
    font-size: 1.1em;
}

.access-log {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.log-entry {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: rgba(67, 233, 123, 0.05);
    border-radius: 6px;
    border-left: 3px solid #43e97b;
}

.log-time {
    font-weight: bold;
    color: #666;
    font-size: 0.9em;
    min-width: 50px;
}

.log-desc {
    flex: 1;
    margin-left: 15px;
    color: #333;
    font-size: 0.9em;
}

.stats-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #43e97b;
    box-shadow: 0 0 10px rgba(67, 233, 123, 0.5);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

/* 图表容器 */
.chart-container {
    width: 100%;
    height: calc(100% - 35px);
    flex: 1;
} 