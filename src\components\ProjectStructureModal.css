/* 项目结构弹窗样式 */
.project-structure-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    animation: fadeIn 0.3s ease-out;
}

.project-structure-modal {
    background: linear-gradient(135deg, 
        rgba(15, 20, 25, 0.95) 0%, 
        rgba(25, 35, 45, 0.95) 50%, 
        rgba(15, 20, 25, 0.95) 100%);
    border: 2px solid rgba(0, 212, 255, 0.3);
    border-radius: 15px;
    width: 90%;
    max-width: 800px;
    max-height: 85vh;
    box-shadow: 
        0 20px 60px rgba(0, 0, 0, 0.5),
        0 0 30px rgba(0, 212, 255, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    animation: slideIn 0.4s ease-out;
    overflow: hidden;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    background: linear-gradient(90deg, 
        rgba(0, 212, 255, 0.1) 0%, 
        rgba(0, 180, 220, 0.05) 100%);
    border-bottom: 1px solid rgba(0, 212, 255, 0.2);
}

.modal-header h2 {
    color: #00d4ff;
    font-size: 24px;
    font-weight: bold;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 10px;
    text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
}

.header-icon {
    font-size: 28px;
    animation: pulse 2s infinite;
}

.close-btn {
    background: rgba(255, 107, 107, 0.2);
    border: 1px solid rgba(255, 107, 107, 0.4);
    color: #ff6b6b;
    width: 35px;
    height: 35px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-btn:hover {
    background: rgba(255, 107, 107, 0.3);
    border-color: #ff6b6b;
    transform: scale(1.1);
    box-shadow: 0 0 15px rgba(255, 107, 107, 0.4);
}

.modal-content {
    padding: 0;
    max-height: calc(85vh - 80px);
    overflow-y: auto;
}

.project-info {
    padding: 20px 25px;
    background: rgba(0, 0, 0, 0.2);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.info-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    font-size: 14px;
}

.info-item:last-child {
    margin-bottom: 0;
}

.info-label {
    color: #999;
    min-width: 80px;
    margin-right: 10px;
}

.info-value {
    color: #fff;
    font-weight: 500;
}

.tree-container {
    padding: 15px 0;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
}

.tree-node {
    user-select: none;
}

.tree-node-content {
    display: flex;
    align-items: center;
    padding: 6px 25px;
    cursor: pointer;
    transition: all 0.2s ease;
    border-left: 3px solid transparent;
    position: relative;
}

.tree-node-content:hover {
    background: rgba(0, 212, 255, 0.1);
    border-left-color: rgba(0, 212, 255, 0.5);
}

.tree-node-content.folder:hover {
    background: rgba(244, 233, 37, 0.1);
    border-left-color: rgba(244, 233, 37, 0.5);
}

.tree-toggle {
    margin-right: 8px;
    font-size: 14px;
    transition: transform 0.2s ease;
    cursor: pointer;
}

.tree-toggle.expanded {
    transform: rotate(0deg);
}

.tree-file-icon {
    margin-right: 8px;
    font-size: 14px;
}

.tree-node-name {
    color: #fff;
    font-weight: 500;
    font-size: 14px;
    margin-right: 10px;
    min-width: fit-content;
}

.tree-node-description {
    color: #999;
    font-size: 12px;
    font-style: italic;
    margin-left: auto;
    opacity: 0.8;
}

.tree-children {
    position: relative;
}

.tree-children::before {
    content: '';
    position: absolute;
    left: 35px;
    top: 0;
    bottom: 0;
    width: 1px;
    background: rgba(255, 255, 255, 0.1);
}

/* 节点类型特殊样式 */
.tree-node-content.system .tree-node-name {
    color: #00d4ff;
    font-weight: 700;
    font-size: 16px;
}

.tree-node-content.module .tree-node-name {
    color: #f4e925;
    font-weight: 600;
    font-size: 15px;
}

.tree-node-content.feature .tree-node-name {
    color: #00ff88;
    font-weight: 500;
}

.tree-node-content.tech .tree-node-name {
    color: #ff6b35;
    font-weight: 500;
}

.tree-node-content.function .tree-node-name {
    color: #a8edea;
    font-weight: 500;
}

.tree-node-content.file .tree-node-name {
    color: #e8e8e8;
}

.tree-node-content.folder .tree-node-name {
    color: #f4e925;
    font-weight: 600;
}

/* 节点悬停效果 */
.tree-node-content.system:hover {
    background: rgba(0, 212, 255, 0.15);
    border-left-color: rgba(0, 212, 255, 0.8);
}

.tree-node-content.module:hover {
    background: rgba(244, 233, 37, 0.15);
    border-left-color: rgba(244, 233, 37, 0.8);
}

.tree-node-content.feature:hover {
    background: rgba(0, 255, 136, 0.15);
    border-left-color: rgba(0, 255, 136, 0.8);
}

.tree-node-content.tech:hover {
    background: rgba(255, 107, 53, 0.15);
    border-left-color: rgba(255, 107, 53, 0.8);
}

.tree-node-content.function:hover {
    background: rgba(168, 237, 234, 0.15);
    border-left-color: rgba(168, 237, 234, 0.8);
}

/* 滚动条样式 */
.modal-content::-webkit-scrollbar {
    width: 8px;
}

.modal-content::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
}

.modal-content::-webkit-scrollbar-thumb {
    background: rgba(0, 212, 255, 0.3);
    border-radius: 4px;
    transition: background 0.3s ease;
}

.modal-content::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 212, 255, 0.5);
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .project-structure-modal {
        width: 95%;
        max-height: 90vh;
        margin: 20px;
    }
    
    .modal-header {
        padding: 15px 20px;
    }
    
    .modal-header h2 {
        font-size: 20px;
    }
    
    .tree-node-content {
        padding: 8px 20px;
    }
    
    .tree-node-name {
        font-size: 13px;
    }
    
    .tree-node-description {
        font-size: 11px;
    }
    
    .project-info {
        padding: 15px 20px;
    }
    
    .info-item {
        flex-direction: column;
        align-items: flex-start;
        margin-bottom: 10px;
    }
    
    .info-label {
        margin-bottom: 2px;
        margin-right: 0;
    }
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
    .project-structure-modal {
        background: linear-gradient(135deg, 
            rgba(10, 15, 20, 0.98) 0%, 
            rgba(20, 30, 40, 0.98) 50%, 
            rgba(10, 15, 20, 0.98) 100%);
    }
}
