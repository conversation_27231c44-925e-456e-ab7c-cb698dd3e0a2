.trading-center-container {
    width: 100%;
    height: 100%;
    padding: 15px;
    background: linear-gradient(135deg, #0f1419 0%, #1a2332 100%);
    color: #fff;
    overflow: hidden;
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    display: flex;
    flex-direction: column;
}

/* 页面标题区域 */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 20px;
    background: linear-gradient(135deg, rgba(0, 212, 255, 0.15) 0%, rgba(0, 150, 255, 0.1) 100%);
    border: 1px solid rgba(0, 212, 255, 0.4);
    border-radius: 12px;
    margin-bottom: 15px;
    backdrop-filter: blur(15px);
    flex-shrink: 0;
    height: 70px;
    box-shadow: 0 8px 32px rgba(0, 212, 255, 0.1);
}

.title-section {
    display: flex;
    align-items: center;
    gap: 15px;
}

.title-icon-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, rgba(0, 212, 255, 0.2) 0%, rgba(0, 150, 255, 0.3) 100%);
    border-radius: 12px;
    border: 1px solid rgba(0, 212, 255, 0.3);
    backdrop-filter: blur(10px);
}

.title-icon-wrapper .title-icon {
    font-size: 28px;
    filter: drop-shadow(0 0 10px rgba(0, 212, 255, 0.8));
}

.title-content {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.page-title {
    font-size: 24px;
    font-weight: 700;
    color: #ffffff;
    text-shadow: 0 0 15px rgba(0, 212, 255, 0.8);
    margin: 0;
    letter-spacing: 1px;
}

.page-subtitle {
    font-size: 12px;
    color: rgba(0, 212, 255, 0.8);
    margin: 0;
    font-weight: 400;
    letter-spacing: 0.5px;
    text-transform: uppercase;
}

.header-stats {
    display: flex;
    gap: 20px;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px 15px;
    background: rgba(0, 212, 255, 0.08);
    border: 1px solid rgba(0, 212, 255, 0.2);
    border-radius: 8px;
    backdrop-filter: blur(10px);
    min-width: 120px;
    transition: all 0.3s ease;
}

.stat-item:hover {
    background: rgba(0, 212, 255, 0.15);
    border-color: rgba(0, 212, 255, 0.4);
}

.stat-icon {
    font-size: 20px;
    filter: drop-shadow(0 0 8px rgba(0, 212, 255, 0.6));
}

.stat-content {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.stat-value {
    font-size: 18px;
    font-weight: bold;
    color: #ffffff;
    text-shadow: 0 0 10px rgba(0, 212, 255, 0.6);
    line-height: 1;
    margin-bottom: 2px;
}

.stat-label {
    font-size: 11px;
    color: rgba(255, 255, 255, 0.7);
    font-weight: 400;
}

/* 视图模式选择器 */
.view-mode-selector {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
    padding: 0 10px;
}

.view-mode-selector .selector-label {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
}

.view-mode-selector .selector-options {
    display: flex;
    gap: 10px;
}

.mode-option {
    padding: 8px 16px;
    background: rgba(0, 212, 255, 0.08);
    border: 1px solid rgba(0, 212, 255, 0.2);
    border-radius: 8px;
    color: rgba(255, 255, 255, 0.7);
    font-size: 13px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 6px;
}

.mode-option:hover {
    background: rgba(0, 212, 255, 0.15);
    border-color: rgba(0, 212, 255, 0.4);
    color: #fff;
    transform: translateY(-1px);
}

.mode-option.active {
    background: rgba(0, 212, 255, 0.25);
    border-color: rgba(0, 212, 255, 0.6);
    color: #fff;
    box-shadow: 0 0 15px rgba(0, 212, 255, 0.4);
}

/* 时间周期选择器 */
.period-selector {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
    padding: 0 10px;
}

.selector-label {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
}

.selector-options {
    display: flex;
    gap: 10px;
}

.period-option {
    padding: 6px 15px;
    background: rgba(0, 212, 255, 0.08);
    border: 1px solid rgba(0, 212, 255, 0.2);
    border-radius: 6px;
    color: rgba(255, 255, 255, 0.7);
    font-size: 13px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.period-option:hover {
    background: rgba(0, 212, 255, 0.15);
    border-color: rgba(0, 212, 255, 0.4);
    color: #fff;
}

.period-option.active {
    background: rgba(0, 212, 255, 0.25);
    border-color: rgba(0, 212, 255, 0.6);
    color: #fff;
    box-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
}

/* 图表网格布局 */
.trading-charts-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(2, 1fr);
    grid-template-areas: 
        "price cost"
        "bid review";
    gap: 20px;
    flex: 1;
    overflow: hidden;
}

/* 图表卡片样式 */
.chart-card {
    background: linear-gradient(135deg, rgba(26, 35, 50, 0.9) 0%, rgba(15, 20, 25, 0.9) 100%);
    border-radius: 15px;
    border: 1px solid rgba(0, 212, 255, 0.3);
    box-shadow: 0 8px 32px rgba(0, 212, 255, 0.1);
    padding: 20px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.chart-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, #00d4ff, transparent);
    animation: scan 2s linear infinite;
}

@keyframes scan {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.card-title {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
    font-size: 16px;
    font-weight: bold;
    color: #00d4ff;
    text-shadow: 0 0 8px rgba(0, 212, 255, 0.5);
}

.card-title .title-icon {
    font-size: 18px;
    filter: drop-shadow(0 0 5px rgba(0, 212, 255, 0.8));
}

.chart-container {
    flex: 1;
    width: 100%;
    height: 100%;
}

/* 发光效果 */
.chart-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(0, 212, 255, 0.1) 50%, transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    border-radius: 15px;
}

.chart-card:hover::after {
    opacity: 1;
}

/* 响应式设计 */
@media (max-width: 1600px) {
    .trading-charts-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 1200px) {
    .trading-charts-grid {
        grid-template-areas: 
            "price price"
            "cost bid"
            "review review";
    }
    
    .chart-card {
        padding: 15px;
    }
    
    .header-stats {
        gap: 20px;
    }
    
    .stat-item {
        padding: 8px 15px;
    }
}

@media (max-width: 768px) {
    .trading-charts-grid {
        grid-template-columns: 1fr;
        grid-template-areas: 
            "price"
            "cost"
            "bid"
            "review";
    }
    
    .page-header {
        flex-direction: column;
        gap: 20px;
        height: auto;
        padding: 15px;
    }
    
    .header-stats {
        flex-wrap: wrap;
        justify-content: center;
        gap: 15px;
    }
    
    .page-title {
        font-size: 20px;
    }
    
    .period-selector {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
}

/* 滚动条美化 */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background: rgba(0, 212, 255, 0.6);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 212, 255, 0.8);
}

/* 动画效果 */
@keyframes dataFlow {
    0% {
        opacity: 0;
        transform: translateY(10px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes heartbeat {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.data-reporting {
    padding: 20px;
    height: 100%;
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.module-header {
    margin-bottom: 30px;
    text-align: center;
}

.module-header h2 {
    color: white;
    font-size: 2.5em;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.module-description {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.1em;
}

.reporting-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 25px;
    margin-top: 30px;
}

.report-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    overflow: hidden;
    transition: transform 0.3s ease;
}

.report-card:hover {
    transform: translateY(-5px);
}

.report-card.primary .card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.report-card.secondary .card-header {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.report-card.accent .card-header {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.report-card.info .card-header {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.card-header {
    color: white;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h3 {
    margin: 0;
    font-size: 1.4em;
    font-weight: 600;
}

.upload-status, .report-status, .status-indicator, .history-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.upload-status.active {
    background: #28a745;
    box-shadow: 0 0 10px rgba(40, 167, 69, 0.5);
}

.report-status.ready {
    background: #17a2b8;
    box-shadow: 0 0 10px rgba(23, 162, 184, 0.5);
}

.status-indicator.normal {
    background: #ffc107;
    box-shadow: 0 0 10px rgba(255, 193, 7, 0.5);
}

.history-indicator {
    background: #6f42c1;
    box-shadow: 0 0 10px rgba(111, 66, 193, 0.5);
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

.card-content {
    padding: 25px;
}

.data-metrics {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-bottom: 20px;
}

.metric-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    background: rgba(102, 126, 234, 0.05);
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.metric-label {
    font-weight: 500;
    color: #666;
}

.metric-value {
    font-weight: bold;
    color: #333;
}

.metric-value.success {
    color: #28a745;
}

.upload-progress {
    margin-top: 20px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    transition: width 0.3s ease;
}

.progress-text {
    font-size: 0.9em;
    color: #666;
    text-align: center;
    display: block;
}

.report-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.report-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: rgba(240, 147, 251, 0.05);
    border-radius: 10px;
    border-left: 4px solid #f093fb;
}

.report-info {
    display: flex;
    flex-direction: column;
}

.report-name {
    font-weight: 600;
    color: #333;
    margin-bottom: 3px;
}

.report-time {
    color: #666;
    font-size: 0.9em;
}

.download-btn {
    padding: 8px 16px;
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    border: none;
    border-radius: 20px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.download-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(240, 147, 251, 0.3);
}

.status-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    background: rgba(79, 172, 254, 0.05);
    border-radius: 8px;
    border-left: 4px solid #4facfe;
}

.status-item.success {
    border-left-color: #28a745;
    background: rgba(40, 167, 69, 0.05);
}

.status-item.warning {
    border-left-color: #ffc107;
    background: rgba(255, 193, 7, 0.05);
}

.status-icon {
    font-weight: bold;
    margin-right: 10px;
}

.status-text {
    flex: 1;
    color: #333;
}

.status-time {
    font-size: 0.9em;
    color: #666;
}

.history-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    margin-bottom: 25px;
}

.stat-card {
    text-align: center;
    padding: 20px;
    background: rgba(67, 233, 123, 0.05);
    border-radius: 10px;
    border-left: 4px solid #43e97b;
}

.stat-number {
    font-size: 2.2em;
    font-weight: bold;
    color: #43e97b;
    margin-bottom: 5px;
}

.stat-label {
    color: #666;
    font-size: 0.9em;
    font-weight: 500;
}

.recent-uploads h4 {
    margin-bottom: 15px;
    color: #333;
    font-size: 1.1em;
}

.upload-log {
    display: flex;
    flex-direction: column;
    gap: 8px;
    max-height: 150px;
    overflow-y: auto;
}

/* 历史交易视图样式 */
.history-view {
    display: flex;
    flex-direction: column;
    gap: 20px;
    flex: 1;
    overflow: hidden;
}

.history-charts {
    height: 350px;
    flex-shrink: 0;
}

.history-charts .chart-card {
    height: 100%;
}

.history-table-card {
    background: linear-gradient(135deg, rgba(26, 35, 50, 0.9) 0%, rgba(15, 20, 25, 0.9) 100%);
    border-radius: 15px;
    border: 1px solid rgba(0, 212, 255, 0.3);
    box-shadow: 0 8px 32px rgba(0, 212, 255, 0.1);
    padding: 20px;
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.history-table {
    flex: 1;
    overflow: auto;
    margin-top: 15px;
}

.history-table table {
    width: 100%;
    border-collapse: collapse;
    color: #fff;
}

.history-table th,
.history-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid rgba(0, 212, 255, 0.2);
}

.history-table th {
    background: rgba(0, 212, 255, 0.1);
    font-weight: 600;
    font-size: 14px;
    color: rgba(0, 212, 255, 0.9);
}

.history-table td {
    font-size: 13px;
}

.history-table tr:hover {
    background: rgba(0, 212, 255, 0.05);
}

.status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.status.success {
    background: rgba(6, 255, 165, 0.2);
    color: #06FFA5;
    border: 1px solid rgba(6, 255, 165, 0.3);
}

.status.failed {
    background: rgba(255, 107, 53, 0.2);
    color: #FF6B35;
    border: 1px solid rgba(255, 107, 53, 0.3);
}

.status.partial {
    background: rgba(255, 210, 63, 0.2);
    color: #FFD23F;
    border: 1px solid rgba(255, 210, 63, 0.3);
}

.penalty-amount {
    color: #FF6B35;
    font-weight: 600;
}

/* 展开按钮样式 */
.expand-btn {
    background: rgba(0, 212, 255, 0.1);
    border: 1px solid rgba(0, 212, 255, 0.3);
    color: #00d4ff;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.3s ease;
}

.expand-btn:hover {
    background: rgba(0, 212, 255, 0.2);
    border-color: rgba(0, 212, 255, 0.5);
}

.penalty-detail-hint {
    font-size: 11px;
    color: rgba(255, 107, 53, 0.7);
    margin-left: 5px;
}

/* 主要行样式 */
.main-row {
    transition: all 0.3s ease;
}

.main-row:hover {
    background: rgba(0, 212, 255, 0.05);
}

/* 详情行样式 */
.detail-row {
    background: rgba(0, 212, 255, 0.02) !important;
}

.detail-row td {
    padding: 0 !important;
    border-bottom: 1px solid rgba(0, 212, 255, 0.1) !important;
}

/* 罚款详情容器 */
.penalty-details {
    padding: 20px;
    margin: 10px;
    background: linear-gradient(135deg, rgba(255, 107, 53, 0.05) 0%, rgba(255, 107, 53, 0.02) 100%);
    border: 1px solid rgba(255, 107, 53, 0.2);
    border-radius: 8px;
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.penalty-details-header {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 16px;
    font-weight: 600;
    color: #FF6B35;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(255, 107, 53, 0.2);
}

.details-icon {
    font-size: 18px;
    filter: drop-shadow(0 0 8px rgba(255, 107, 53, 0.6));
}

.penalty-details-content {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

/* 罚款项目样式 */
.penalty-item {
    background: rgba(255, 107, 53, 0.08);
    border: 1px solid rgba(255, 107, 53, 0.15);
    border-radius: 6px;
    padding: 12px;
    transition: all 0.3s ease;
}

.penalty-item:hover {
    background: rgba(255, 107, 53, 0.12);
    border-color: rgba(255, 107, 53, 0.25);
}

.penalty-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.penalty-type {
    font-weight: 600;
    color: #FF6B35;
    font-size: 14px;
}

.penalty-amount-detail {
    font-weight: bold;
    color: #FF6B35;
    font-size: 14px;
    text-shadow: 0 0 8px rgba(255, 107, 53, 0.4);
}

.penalty-reason {
    font-size: 13px;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.4;
}

/* 罚款汇总样式 */
.penalty-summary {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid rgba(255, 107, 53, 0.2);
    background: rgba(255, 107, 53, 0.05);
    border-radius: 6px;
    padding: 15px;
}

.summary-line {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    font-size: 14px;
}

.summary-line:last-child {
    margin-bottom: 0;
}

.summary-line span:first-child {
    color: rgba(255, 255, 255, 0.8);
}

.summary-line span:last-child {
    font-weight: 600;
    color: #fff;
}

.total-penalty {
    color: #FF6B35 !important;
    font-size: 16px !important;
    font-weight: bold !important;
    text-shadow: 0 0 10px rgba(255, 107, 53, 0.6) !important;
}



    .log-item {
    padding: 8px 12px;
    background: rgba(67, 233, 123, 0.05);
    border-radius: 6px;
    border-left: 3px solid #43e97b;
    font-size: 0.9em;
    color: #333;
} 